import { Question, DifficultyLevel, Level } from '../types';

export const LEVELS: Level[] = [
  // 级别1：10以内加法（合计≤10）
  {
    id: 'level-1-1',
    name: '10以内加法 - 第1关',
    description: '两个数相加，结果不超过10',
    difficultyId: 'level-1',
    stage: 1,
    operandRange: [1, 9],
    operators: ['+'],
    resultLimit: 10,
    allowCarry: false,
  },
  {
    id: 'level-1-2',
    name: '10以内加法 - 第2关',
    description: '两个数相加，结果不超过10',
    difficultyId: 'level-1',
    stage: 2,
    operandRange: [1, 9],
    operators: ['+'],
    resultLimit: 10,
    allowCarry: false,
  },
  // 级别2：20以内加法（合计≤20）
  {
    id: 'level-2-1',
    name: '20以内加法 - 第1关',
    description: '两个数相加，结果不超过20',
    difficultyId: 'level-2',
    stage: 1,
    operandRange: [1, 19],
    operators: ['+'],
    resultLimit: 20,
    allowCarry: true,
  },
  {
    id: 'level-2-2',
    name: '20以内加法 - 第2关',
    description: '两个数相加，结果不超过20',
    difficultyId: 'level-2',
    stage: 2,
    operandRange: [1, 19],
    operators: ['+'],
    resultLimit: 20,
    allowCarry: true,
  },
  // 级别3：10以内加减法（加法合计≤10）
  {
    id: 'level-3-1',
    name: '10以内加减法 - 第1关',
    description: '10以内的加法和减法运算',
    difficultyId: 'level-3',
    stage: 1,
    operandRange: [1, 9],
    operators: ['+', '-'],
    resultLimit: 10,
    allowCarry: false,
    allowBorrow: false,
  },
  {
    id: 'level-3-2',
    name: '10以内加减法 - 第2关',
    description: '10以内的加法和减法运算',
    difficultyId: 'level-3',
    stage: 2,
    operandRange: [1, 10],
    operators: ['+', '-'],
    resultLimit: 10,
    allowCarry: false,
    allowBorrow: false,
  },
  // 级别4：20以内加减法（加法合计≤20）
  {
    id: 'level-4-1',
    name: '20以内加减法 - 第1关',
    description: '20以内的加法和减法运算',
    difficultyId: 'level-4',
    stage: 1,
    operandRange: [1, 19],
    operators: ['+', '-'],
    resultLimit: 20,
    allowCarry: true,
    allowBorrow: true,
  },
  {
    id: 'level-4-2',
    name: '20以内加减法 - 第2关',
    description: '20以内的加法和减法运算',
    difficultyId: 'level-4',
    stage: 2,
    operandRange: [1, 20],
    operators: ['+', '-'],
    resultLimit: 20,
    allowCarry: true,
    allowBorrow: true,
  },
  // 级别5：100以内加法
  {
    id: 'level-5-1',
    name: '100以内加法 - 第1关',
    description: '简单的100以内加法练习',
    difficultyId: 'level-5',
    stage: 1,
    operandRange: [1, 50],
    operators: ['+'],
    allowCarry: true,
  },
  {
    id: 'level-5-2',
    name: '100以内加法 - 第2关',
    description: '进阶的100以内加法练习',
    difficultyId: 'level-5',
    stage: 2,
    operandRange: [1, 100],
    operators: ['+'],
    allowCarry: true,
  },
  // 级别6：100以内加减法
  {
    id: 'level-6-1',
    name: '100以内加减法 - 第1关',
    description: '简单的100以内加减法练习',
    difficultyId: 'level-6',
    stage: 1,
    operandRange: [1, 50],
    operators: ['+', '-'],
    allowCarry: true,
    allowBorrow: true,
  },
  {
    id: 'level-6-2',
    name: '100以内加减法 - 第2关',
    description: '进阶的100以内加减法练习',
    difficultyId: 'level-6',
    stage: 2,
    operandRange: [1, 100],
    operators: ['+', '-'],
    allowCarry: true,
    allowBorrow: true,
  },
];

export const DIFFICULTY_LEVELS: DifficultyLevel[] = [
  {
    id: 'level-1',
    name: '10以内加法',
    description: '两个数相加，结果不超过10',
    operandRange: [1, 10],
    operators: ['+'],
    resultLimit: 10,
    allowCarry: false,
  },
  {
    id: 'level-2',
    name: '20以内加法',
    description: '两个数相加，结果不超过20',
    operandRange: [1, 20],
    operators: ['+'],
    resultLimit: 20,
    allowCarry: true,
  },
  {
    id: 'level-3',
    name: '10以内加减法',
    description: '10以内的加法和减法运算',
    operandRange: [1, 10],
    operators: ['+', '-'],
    allowCarry: false,
    allowBorrow: false,
  },
  {
    id: 'level-4',
    name: '20以内加减法',
    description: '20以内的加法和减法运算',
    operandRange: [1, 20],
    operators: ['+', '-'],
    allowCarry: true,
    allowBorrow: true,
  },
  {
    id: 'level-5',
    name: '100以内加法',
    description: '100以内的加法运算',
    operandRange: [1, 100],
    operators: ['+'],
    allowCarry: true,
  },
  {
    id: 'level-6',
    name: '100以内加减法',
    description: '100以内的加法和减法运算',
    operandRange: [1, 100],
    operators: ['+', '-'],
    allowCarry: true,
    allowBorrow: true,
  },
];

function generateRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function generateAdditionQuestion(
  operandRange: [number, number],
  resultLimit?: number
): Question {
  const [min, max] = operandRange;
  let operand1: number;
  let operand2: number;
  let answer: number;

  do {
    operand1 = generateRandomNumber(min, max);
    operand2 = generateRandomNumber(min, max);
    answer = operand1 + operand2;
  } while (resultLimit && answer > resultLimit);

  return {
    id: `add_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    operand1,
    operand2,
    operator: '+',
    answer,
  };
}

function generateSubtractionQuestion(
  operandRange: [number, number],
  resultLimit?: number
): Question {
  const [min, max] = operandRange;
  let operand1: number;
  let operand2: number;
  let answer: number;

  do {
    operand1 = generateRandomNumber(min, max);
    operand2 = generateRandomNumber(min, operand1);
    answer = operand1 - operand2;
  } while (resultLimit && answer > resultLimit || answer < 0);

  return {
    id: `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    operand1,
    operand2,
    operator: '-',
    answer,
  };
}

export function generateQuestion(difficultyId: string): Question {
  const difficulty = DIFFICULTY_LEVELS.find(d => d.id === difficultyId);
  if (!difficulty) {
    throw new Error(`Unknown difficulty level: ${difficultyId}`);
  }

  const operator = difficulty.operators[
    Math.floor(Math.random() * difficulty.operators.length)
  ];

  if (operator === '+') {
    return generateAdditionQuestion(difficulty.operandRange, difficulty.resultLimit);
  } else {
    return generateSubtractionQuestion(difficulty.operandRange, difficulty.resultLimit);
  }
}

export function generateQuestionForLevel(levelId: string): Question {
  const level = LEVELS.find(l => l.id === levelId);
  if (!level) {
    throw new Error(`Unknown level: ${levelId}`);
  }

  const operator = level.operators[
    Math.floor(Math.random() * level.operators.length)
  ];

  if (operator === '+') {
    return generateAdditionQuestion(level.operandRange, level.resultLimit);
  } else {
    return generateSubtractionQuestion(level.operandRange, level.resultLimit);
  }
}

export function generateQuestionsForLevel(levelId: string, count: number): Question[] {
  const questions: Question[] = [];
  for (let i = 0; i < count; i++) {
    questions.push(generateQuestionForLevel(levelId));
  }
  return questions;
}

export function getLevelsByDifficulty(difficultyId: string): Level[] {
  return LEVELS.filter(level => level.difficultyId === difficultyId);
}

export function generateQuestions(difficultyId: string, count: number): Question[] {
  const questions: Question[] = [];
  for (let i = 0; i < count; i++) {
    questions.push(generateQuestion(difficultyId));
  }
  return questions;
}

export function checkAnswer(question: Question, userAnswer: number): boolean {
  return question.answer === userAnswer;
}

export function calculateScore(questions: Question[]): {
  correct: number;
  total: number;
  accuracy: number;
  score: number;
} {
  const total = questions.length;
  const correct = questions.filter(q => q.isCorrect === true).length;
  const accuracy = total > 0 ? (correct / total) * 100 : 0;
  const score = Math.round(accuracy);

  return { correct, total, accuracy, score };
}