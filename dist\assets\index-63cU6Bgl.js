var xm=Object.defineProperty;var wm=(e,t,n)=>t in e?xm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var jt=(e,t,n)=>wm(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Sm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var _d={exports:{}},js={},Id={exports:{}},z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ni=Symbol.for("react.element"),Cm=Symbol.for("react.portal"),km=Symbol.for("react.fragment"),Pm=Symbol.for("react.strict_mode"),Tm=Symbol.for("react.profiler"),Em=Symbol.for("react.provider"),jm=Symbol.for("react.context"),Nm=Symbol.for("react.forward_ref"),Lm=Symbol.for("react.suspense"),Am=Symbol.for("react.memo"),Rm=Symbol.for("react.lazy"),xu=Symbol.iterator;function Mm(e){return e===null||typeof e!="object"?null:(e=xu&&e[xu]||e["@@iterator"],typeof e=="function"?e:null)}var Od={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Fd=Object.assign,zd={};function sr(e,t,n){this.props=e,this.context=t,this.refs=zd,this.updater=n||Od}sr.prototype.isReactComponent={};sr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};sr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Bd(){}Bd.prototype=sr.prototype;function Ka(e,t,n){this.props=e,this.context=t,this.refs=zd,this.updater=n||Od}var Ya=Ka.prototype=new Bd;Ya.constructor=Ka;Fd(Ya,sr.prototype);Ya.isPureReactComponent=!0;var wu=Array.isArray,Ud=Object.prototype.hasOwnProperty,Xa={current:null},$d={key:!0,ref:!0,__self:!0,__source:!0};function Wd(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)Ud.call(t,r)&&!$d.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:ni,type:e,key:s,ref:o,props:i,_owner:Xa.current}}function Vm(e,t){return{$$typeof:ni,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Za(e){return typeof e=="object"&&e!==null&&e.$$typeof===ni}function Dm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Su=/\/+/g;function Js(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Dm(""+e.key):t.toString(36)}function Di(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ni:case Cm:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Js(o,0):r,wu(i)?(n="",e!=null&&(n=e.replace(Su,"$&/")+"/"),Di(i,t,n,"",function(u){return u})):i!=null&&(Za(i)&&(i=Vm(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Su,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",wu(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+Js(s,a);o+=Di(s,t,n,l,i)}else if(l=Mm(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+Js(s,a++),o+=Di(s,t,n,l,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function fi(e,t,n){if(e==null)return e;var r=[],i=0;return Di(e,r,"","",function(s){return t.call(n,s,i++)}),r}function _m(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var je={current:null},_i={transition:null},Im={ReactCurrentDispatcher:je,ReactCurrentBatchConfig:_i,ReactCurrentOwner:Xa};function bd(){throw Error("act(...) is not supported in production builds of React.")}z.Children={map:fi,forEach:function(e,t,n){fi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return fi(e,function(){t++}),t},toArray:function(e){return fi(e,function(t){return t})||[]},only:function(e){if(!Za(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};z.Component=sr;z.Fragment=km;z.Profiler=Tm;z.PureComponent=Ka;z.StrictMode=Pm;z.Suspense=Lm;z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Im;z.act=bd;z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Fd({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Xa.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Ud.call(t,l)&&!$d.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ni,type:e.type,key:i,ref:s,props:r,_owner:o}};z.createContext=function(e){return e={$$typeof:jm,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Em,_context:e},e.Consumer=e};z.createElement=Wd;z.createFactory=function(e){var t=Wd.bind(null,e);return t.type=e,t};z.createRef=function(){return{current:null}};z.forwardRef=function(e){return{$$typeof:Nm,render:e}};z.isValidElement=Za;z.lazy=function(e){return{$$typeof:Rm,_payload:{_status:-1,_result:e},_init:_m}};z.memo=function(e,t){return{$$typeof:Am,type:e,compare:t===void 0?null:t}};z.startTransition=function(e){var t=_i.transition;_i.transition={};try{e()}finally{_i.transition=t}};z.unstable_act=bd;z.useCallback=function(e,t){return je.current.useCallback(e,t)};z.useContext=function(e){return je.current.useContext(e)};z.useDebugValue=function(){};z.useDeferredValue=function(e){return je.current.useDeferredValue(e)};z.useEffect=function(e,t){return je.current.useEffect(e,t)};z.useId=function(){return je.current.useId()};z.useImperativeHandle=function(e,t,n){return je.current.useImperativeHandle(e,t,n)};z.useInsertionEffect=function(e,t){return je.current.useInsertionEffect(e,t)};z.useLayoutEffect=function(e,t){return je.current.useLayoutEffect(e,t)};z.useMemo=function(e,t){return je.current.useMemo(e,t)};z.useReducer=function(e,t,n){return je.current.useReducer(e,t,n)};z.useRef=function(e){return je.current.useRef(e)};z.useState=function(e){return je.current.useState(e)};z.useSyncExternalStore=function(e,t,n){return je.current.useSyncExternalStore(e,t,n)};z.useTransition=function(){return je.current.useTransition()};z.version="18.3.1";Id.exports=z;var S=Id.exports;const Ja=Sm(S);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Om=S,Fm=Symbol.for("react.element"),zm=Symbol.for("react.fragment"),Bm=Object.prototype.hasOwnProperty,Um=Om.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,$m={key:!0,ref:!0,__self:!0,__source:!0};function Hd(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Bm.call(t,r)&&!$m.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Fm,type:e,key:s,ref:o,props:i,_owner:Um.current}}js.Fragment=zm;js.jsx=Hd;js.jsxs=Hd;_d.exports=js;var h=_d.exports,Bo={},Qd={exports:{}},$e={},Gd={exports:{}},Kd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,R){var _=T.length;T.push(R);e:for(;0<_;){var L=_-1>>>1,D=T[L];if(0<i(D,R))T[L]=R,T[_]=D,_=L;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var R=T[0],_=T.pop();if(_!==R){T[0]=_;e:for(var L=0,D=T.length,re=D>>>1;L<re;){var ve=2*(L+1)-1,Tn=T[ve],Ie=ve+1,en=T[Ie];if(0>i(Tn,_))Ie<D&&0>i(en,Tn)?(T[L]=en,T[Ie]=_,L=Ie):(T[L]=Tn,T[ve]=_,L=ve);else if(Ie<D&&0>i(en,_))T[L]=en,T[Ie]=_,L=Ie;else break e}}return R}function i(T,R){var _=T.sortIndex-R.sortIndex;return _!==0?_:T.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,d=null,f=3,g=!1,v=!1,x=!1,P=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(T){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=T)r(u),R.sortIndex=R.expirationTime,t(l,R);else break;R=n(u)}}function w(T){if(x=!1,m(T),!v)if(n(l)!==null)v=!0,H(C);else{var R=n(u);R!==null&&ue(w,R.startTime-T)}}function C(T,R){v=!1,x&&(x=!1,y(k),k=-1),g=!0;var _=f;try{for(m(R),d=n(l);d!==null&&(!(d.expirationTime>R)||T&&!b());){var L=d.callback;if(typeof L=="function"){d.callback=null,f=d.priorityLevel;var D=L(d.expirationTime<=R);R=e.unstable_now(),typeof D=="function"?d.callback=D:d===n(l)&&r(l),m(R)}else r(l);d=n(l)}if(d!==null)var re=!0;else{var ve=n(u);ve!==null&&ue(w,ve.startTime-R),re=!1}return re}finally{d=null,f=_,g=!1}}var E=!1,j=null,k=-1,A=5,V=-1;function b(){return!(e.unstable_now()-V<A)}function F(){if(j!==null){var T=e.unstable_now();V=T;var R=!0;try{R=j(!0,T)}finally{R?J():(E=!1,j=null)}}else E=!1}var J;if(typeof p=="function")J=function(){p(F)};else if(typeof MessageChannel<"u"){var K=new MessageChannel,Le=K.port2;K.port1.onmessage=F,J=function(){Le.postMessage(null)}}else J=function(){P(F,0)};function H(T){j=T,E||(E=!0,J())}function ue(T,R){k=P(function(){T(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,H(C))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(T){switch(f){case 1:case 2:case 3:var R=3;break;default:R=f}var _=f;f=R;try{return T()}finally{f=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,R){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var _=f;f=T;try{return R()}finally{f=_}},e.unstable_scheduleCallback=function(T,R,_){var L=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?L+_:L):_=L,T){case 1:var D=-1;break;case 2:D=250;break;case 5:D=**********;break;case 4:D=1e4;break;default:D=5e3}return D=_+D,T={id:c++,callback:R,priorityLevel:T,startTime:_,expirationTime:D,sortIndex:-1},_>L?(T.sortIndex=_,t(u,T),n(l)===null&&T===n(u)&&(x?(y(k),k=-1):x=!0,ue(w,_-L))):(T.sortIndex=D,t(l,T),v||g||(v=!0,H(C))),T},e.unstable_shouldYield=b,e.unstable_wrapCallback=function(T){var R=f;return function(){var _=f;f=R;try{return T.apply(this,arguments)}finally{f=_}}}})(Kd);Gd.exports=Kd;var Wm=Gd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bm=S,Be=Wm;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Yd=new Set,Ir={};function Sn(e,t){Yn(e,t),Yn(e+"Capture",t)}function Yn(e,t){for(Ir[e]=t,e=0;e<t.length;e++)Yd.add(t[e])}var St=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Uo=Object.prototype.hasOwnProperty,Hm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Cu={},ku={};function Qm(e){return Uo.call(ku,e)?!0:Uo.call(Cu,e)?!1:Hm.test(e)?ku[e]=!0:(Cu[e]=!0,!1)}function Gm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Km(e,t,n,r){if(t===null||typeof t>"u"||Gm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ne(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ye[e]=new Ne(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ye[t]=new Ne(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ye[e]=new Ne(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ye[e]=new Ne(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ye[e]=new Ne(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ye[e]=new Ne(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ye[e]=new Ne(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ye[e]=new Ne(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ye[e]=new Ne(e,5,!1,e.toLowerCase(),null,!1,!1)});var qa=/[\-:]([a-z])/g;function el(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(qa,el);ye[t]=new Ne(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(qa,el);ye[t]=new Ne(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(qa,el);ye[t]=new Ne(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ye[e]=new Ne(e,1,!1,e.toLowerCase(),null,!1,!1)});ye.xlinkHref=new Ne("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ye[e]=new Ne(e,1,!1,e.toLowerCase(),null,!0,!0)});function tl(e,t,n,r){var i=ye.hasOwnProperty(t)?ye[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Km(t,n,i,r)&&(n=null),r||i===null?Qm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Et=bm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,hi=Symbol.for("react.element"),jn=Symbol.for("react.portal"),Nn=Symbol.for("react.fragment"),nl=Symbol.for("react.strict_mode"),$o=Symbol.for("react.profiler"),Xd=Symbol.for("react.provider"),Zd=Symbol.for("react.context"),rl=Symbol.for("react.forward_ref"),Wo=Symbol.for("react.suspense"),bo=Symbol.for("react.suspense_list"),il=Symbol.for("react.memo"),At=Symbol.for("react.lazy"),Jd=Symbol.for("react.offscreen"),Pu=Symbol.iterator;function lr(e){return e===null||typeof e!="object"?null:(e=Pu&&e[Pu]||e["@@iterator"],typeof e=="function"?e:null)}var ne=Object.assign,qs;function vr(e){if(qs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);qs=t&&t[1]||""}return`
`+qs+e}var eo=!1;function to(e,t){if(!e||eo)return"";eo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{eo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?vr(e):""}function Ym(e){switch(e.tag){case 5:return vr(e.type);case 16:return vr("Lazy");case 13:return vr("Suspense");case 19:return vr("SuspenseList");case 0:case 2:case 15:return e=to(e.type,!1),e;case 11:return e=to(e.type.render,!1),e;case 1:return e=to(e.type,!0),e;default:return""}}function Ho(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Nn:return"Fragment";case jn:return"Portal";case $o:return"Profiler";case nl:return"StrictMode";case Wo:return"Suspense";case bo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Zd:return(e.displayName||"Context")+".Consumer";case Xd:return(e._context.displayName||"Context")+".Provider";case rl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case il:return t=e.displayName||null,t!==null?t:Ho(e.type)||"Memo";case At:t=e._payload,e=e._init;try{return Ho(e(t))}catch{}}return null}function Xm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ho(t);case 8:return t===nl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Qt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function qd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Zm(e){var t=qd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function pi(e){e._valueTracker||(e._valueTracker=Zm(e))}function ef(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=qd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ki(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Qo(e,t){var n=t.checked;return ne({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Qt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function tf(e,t){t=t.checked,t!=null&&tl(e,"checked",t,!1)}function Go(e,t){tf(e,t);var n=Qt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ko(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ko(e,t.type,Qt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Eu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ko(e,t,n){(t!=="number"||Ki(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var xr=Array.isArray;function Wn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Qt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Yo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return ne({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ju(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(N(92));if(xr(n)){if(1<n.length)throw Error(N(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Qt(n)}}function nf(e,t){var n=Qt(t.value),r=Qt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Nu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function rf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Xo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?rf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var mi,sf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(mi=mi||document.createElement("div"),mi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=mi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Or(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var kr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Jm=["Webkit","ms","Moz","O"];Object.keys(kr).forEach(function(e){Jm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),kr[t]=kr[e]})});function of(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||kr.hasOwnProperty(e)&&kr[e]?(""+t).trim():t+"px"}function af(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=of(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var qm=ne({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Zo(e,t){if(t){if(qm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function Jo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var qo=null;function sl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ea=null,bn=null,Hn=null;function Lu(e){if(e=si(e)){if(typeof ea!="function")throw Error(N(280));var t=e.stateNode;t&&(t=Ms(t),ea(e.stateNode,e.type,t))}}function lf(e){bn?Hn?Hn.push(e):Hn=[e]:bn=e}function uf(){if(bn){var e=bn,t=Hn;if(Hn=bn=null,Lu(e),t)for(e=0;e<t.length;e++)Lu(t[e])}}function cf(e,t){return e(t)}function df(){}var no=!1;function ff(e,t,n){if(no)return e(t,n);no=!0;try{return cf(e,t,n)}finally{no=!1,(bn!==null||Hn!==null)&&(df(),uf())}}function Fr(e,t){var n=e.stateNode;if(n===null)return null;var r=Ms(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(N(231,t,typeof n));return n}var ta=!1;if(St)try{var ur={};Object.defineProperty(ur,"passive",{get:function(){ta=!0}}),window.addEventListener("test",ur,ur),window.removeEventListener("test",ur,ur)}catch{ta=!1}function eg(e,t,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Pr=!1,Yi=null,Xi=!1,na=null,tg={onError:function(e){Pr=!0,Yi=e}};function ng(e,t,n,r,i,s,o,a,l){Pr=!1,Yi=null,eg.apply(tg,arguments)}function rg(e,t,n,r,i,s,o,a,l){if(ng.apply(this,arguments),Pr){if(Pr){var u=Yi;Pr=!1,Yi=null}else throw Error(N(198));Xi||(Xi=!0,na=u)}}function Cn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function hf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Au(e){if(Cn(e)!==e)throw Error(N(188))}function ig(e){var t=e.alternate;if(!t){if(t=Cn(e),t===null)throw Error(N(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Au(i),e;if(s===r)return Au(i),t;s=s.sibling}throw Error(N(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(N(189))}}if(n.alternate!==r)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?e:t}function pf(e){return e=ig(e),e!==null?mf(e):null}function mf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=mf(e);if(t!==null)return t;e=e.sibling}return null}var gf=Be.unstable_scheduleCallback,Ru=Be.unstable_cancelCallback,sg=Be.unstable_shouldYield,og=Be.unstable_requestPaint,se=Be.unstable_now,ag=Be.unstable_getCurrentPriorityLevel,ol=Be.unstable_ImmediatePriority,yf=Be.unstable_UserBlockingPriority,Zi=Be.unstable_NormalPriority,lg=Be.unstable_LowPriority,vf=Be.unstable_IdlePriority,Ns=null,ut=null;function ug(e){if(ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(Ns,e,void 0,(e.current.flags&128)===128)}catch{}}var nt=Math.clz32?Math.clz32:fg,cg=Math.log,dg=Math.LN2;function fg(e){return e>>>=0,e===0?32:31-(cg(e)/dg|0)|0}var gi=64,yi=4194304;function wr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ji(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=wr(a):(s&=o,s!==0&&(r=wr(s)))}else o=n&~i,o!==0?r=wr(o):s!==0&&(r=wr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-nt(t),i=1<<n,r|=e[n],t&=~i;return r}function hg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function pg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-nt(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=hg(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function ra(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function xf(){var e=gi;return gi<<=1,!(gi&4194240)&&(gi=64),e}function ro(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ri(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-nt(t),e[t]=n}function mg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-nt(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function al(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-nt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var W=0;function wf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Sf,ll,Cf,kf,Pf,ia=!1,vi=[],Ot=null,Ft=null,zt=null,zr=new Map,Br=new Map,Vt=[],gg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mu(e,t){switch(e){case"focusin":case"focusout":Ot=null;break;case"dragenter":case"dragleave":Ft=null;break;case"mouseover":case"mouseout":zt=null;break;case"pointerover":case"pointerout":zr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Br.delete(t.pointerId)}}function cr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=si(t),t!==null&&ll(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function yg(e,t,n,r,i){switch(t){case"focusin":return Ot=cr(Ot,e,t,n,r,i),!0;case"dragenter":return Ft=cr(Ft,e,t,n,r,i),!0;case"mouseover":return zt=cr(zt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return zr.set(s,cr(zr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Br.set(s,cr(Br.get(s)||null,e,t,n,r,i)),!0}return!1}function Tf(e){var t=un(e.target);if(t!==null){var n=Cn(t);if(n!==null){if(t=n.tag,t===13){if(t=hf(n),t!==null){e.blockedOn=t,Pf(e.priority,function(){Cf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ii(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=sa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);qo=r,n.target.dispatchEvent(r),qo=null}else return t=si(n),t!==null&&ll(t),e.blockedOn=n,!1;t.shift()}return!0}function Vu(e,t,n){Ii(e)&&n.delete(t)}function vg(){ia=!1,Ot!==null&&Ii(Ot)&&(Ot=null),Ft!==null&&Ii(Ft)&&(Ft=null),zt!==null&&Ii(zt)&&(zt=null),zr.forEach(Vu),Br.forEach(Vu)}function dr(e,t){e.blockedOn===t&&(e.blockedOn=null,ia||(ia=!0,Be.unstable_scheduleCallback(Be.unstable_NormalPriority,vg)))}function Ur(e){function t(i){return dr(i,e)}if(0<vi.length){dr(vi[0],e);for(var n=1;n<vi.length;n++){var r=vi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ot!==null&&dr(Ot,e),Ft!==null&&dr(Ft,e),zt!==null&&dr(zt,e),zr.forEach(t),Br.forEach(t),n=0;n<Vt.length;n++)r=Vt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Vt.length&&(n=Vt[0],n.blockedOn===null);)Tf(n),n.blockedOn===null&&Vt.shift()}var Qn=Et.ReactCurrentBatchConfig,qi=!0;function xg(e,t,n,r){var i=W,s=Qn.transition;Qn.transition=null;try{W=1,ul(e,t,n,r)}finally{W=i,Qn.transition=s}}function wg(e,t,n,r){var i=W,s=Qn.transition;Qn.transition=null;try{W=4,ul(e,t,n,r)}finally{W=i,Qn.transition=s}}function ul(e,t,n,r){if(qi){var i=sa(e,t,n,r);if(i===null)po(e,t,r,es,n),Mu(e,r);else if(yg(i,e,t,n,r))r.stopPropagation();else if(Mu(e,r),t&4&&-1<gg.indexOf(e)){for(;i!==null;){var s=si(i);if(s!==null&&Sf(s),s=sa(e,t,n,r),s===null&&po(e,t,r,es,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else po(e,t,r,null,n)}}var es=null;function sa(e,t,n,r){if(es=null,e=sl(r),e=un(e),e!==null)if(t=Cn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=hf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return es=e,null}function Ef(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ag()){case ol:return 1;case yf:return 4;case Zi:case lg:return 16;case vf:return 536870912;default:return 16}default:return 16}}var _t=null,cl=null,Oi=null;function jf(){if(Oi)return Oi;var e,t=cl,n=t.length,r,i="value"in _t?_t.value:_t.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return Oi=i.slice(e,1<r?1-r:void 0)}function Fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function xi(){return!0}function Du(){return!1}function We(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?xi:Du,this.isPropagationStopped=Du,this}return ne(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=xi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=xi)},persist:function(){},isPersistent:xi}),t}var or={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},dl=We(or),ii=ne({},or,{view:0,detail:0}),Sg=We(ii),io,so,fr,Ls=ne({},ii,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fr&&(fr&&e.type==="mousemove"?(io=e.screenX-fr.screenX,so=e.screenY-fr.screenY):so=io=0,fr=e),io)},movementY:function(e){return"movementY"in e?e.movementY:so}}),_u=We(Ls),Cg=ne({},Ls,{dataTransfer:0}),kg=We(Cg),Pg=ne({},ii,{relatedTarget:0}),oo=We(Pg),Tg=ne({},or,{animationName:0,elapsedTime:0,pseudoElement:0}),Eg=We(Tg),jg=ne({},or,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ng=We(jg),Lg=ne({},or,{data:0}),Iu=We(Lg),Ag={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Rg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Mg[e])?!!t[e]:!1}function fl(){return Vg}var Dg=ne({},ii,{key:function(e){if(e.key){var t=Ag[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Rg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fl,charCode:function(e){return e.type==="keypress"?Fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),_g=We(Dg),Ig=ne({},Ls,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ou=We(Ig),Og=ne({},ii,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fl}),Fg=We(Og),zg=ne({},or,{propertyName:0,elapsedTime:0,pseudoElement:0}),Bg=We(zg),Ug=ne({},Ls,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$g=We(Ug),Wg=[9,13,27,32],hl=St&&"CompositionEvent"in window,Tr=null;St&&"documentMode"in document&&(Tr=document.documentMode);var bg=St&&"TextEvent"in window&&!Tr,Nf=St&&(!hl||Tr&&8<Tr&&11>=Tr),Fu=" ",zu=!1;function Lf(e,t){switch(e){case"keyup":return Wg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Af(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ln=!1;function Hg(e,t){switch(e){case"compositionend":return Af(t);case"keypress":return t.which!==32?null:(zu=!0,Fu);case"textInput":return e=t.data,e===Fu&&zu?null:e;default:return null}}function Qg(e,t){if(Ln)return e==="compositionend"||!hl&&Lf(e,t)?(e=jf(),Oi=cl=_t=null,Ln=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nf&&t.locale!=="ko"?null:t.data;default:return null}}var Gg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Gg[e.type]:t==="textarea"}function Rf(e,t,n,r){lf(r),t=ts(t,"onChange"),0<t.length&&(n=new dl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Er=null,$r=null;function Kg(e){$f(e,0)}function As(e){var t=Mn(e);if(ef(t))return e}function Yg(e,t){if(e==="change")return t}var Mf=!1;if(St){var ao;if(St){var lo="oninput"in document;if(!lo){var Uu=document.createElement("div");Uu.setAttribute("oninput","return;"),lo=typeof Uu.oninput=="function"}ao=lo}else ao=!1;Mf=ao&&(!document.documentMode||9<document.documentMode)}function $u(){Er&&(Er.detachEvent("onpropertychange",Vf),$r=Er=null)}function Vf(e){if(e.propertyName==="value"&&As($r)){var t=[];Rf(t,$r,e,sl(e)),ff(Kg,t)}}function Xg(e,t,n){e==="focusin"?($u(),Er=t,$r=n,Er.attachEvent("onpropertychange",Vf)):e==="focusout"&&$u()}function Zg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return As($r)}function Jg(e,t){if(e==="click")return As(t)}function qg(e,t){if(e==="input"||e==="change")return As(t)}function e0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var st=typeof Object.is=="function"?Object.is:e0;function Wr(e,t){if(st(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Uo.call(t,i)||!st(e[i],t[i]))return!1}return!0}function Wu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function bu(e,t){var n=Wu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wu(n)}}function Df(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Df(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function _f(){for(var e=window,t=Ki();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ki(e.document)}return t}function pl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function t0(e){var t=_f(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Df(n.ownerDocument.documentElement,n)){if(r!==null&&pl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=bu(n,s);var o=bu(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var n0=St&&"documentMode"in document&&11>=document.documentMode,An=null,oa=null,jr=null,aa=!1;function Hu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;aa||An==null||An!==Ki(r)||(r=An,"selectionStart"in r&&pl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),jr&&Wr(jr,r)||(jr=r,r=ts(oa,"onSelect"),0<r.length&&(t=new dl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=An)))}function wi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Rn={animationend:wi("Animation","AnimationEnd"),animationiteration:wi("Animation","AnimationIteration"),animationstart:wi("Animation","AnimationStart"),transitionend:wi("Transition","TransitionEnd")},uo={},If={};St&&(If=document.createElement("div").style,"AnimationEvent"in window||(delete Rn.animationend.animation,delete Rn.animationiteration.animation,delete Rn.animationstart.animation),"TransitionEvent"in window||delete Rn.transitionend.transition);function Rs(e){if(uo[e])return uo[e];if(!Rn[e])return e;var t=Rn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in If)return uo[e]=t[n];return e}var Of=Rs("animationend"),Ff=Rs("animationiteration"),zf=Rs("animationstart"),Bf=Rs("transitionend"),Uf=new Map,Qu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Xt(e,t){Uf.set(e,t),Sn(t,[e])}for(var co=0;co<Qu.length;co++){var fo=Qu[co],r0=fo.toLowerCase(),i0=fo[0].toUpperCase()+fo.slice(1);Xt(r0,"on"+i0)}Xt(Of,"onAnimationEnd");Xt(Ff,"onAnimationIteration");Xt(zf,"onAnimationStart");Xt("dblclick","onDoubleClick");Xt("focusin","onFocus");Xt("focusout","onBlur");Xt(Bf,"onTransitionEnd");Yn("onMouseEnter",["mouseout","mouseover"]);Yn("onMouseLeave",["mouseout","mouseover"]);Yn("onPointerEnter",["pointerout","pointerover"]);Yn("onPointerLeave",["pointerout","pointerover"]);Sn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Sn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Sn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Sn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Sn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Sn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Sr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),s0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Sr));function Gu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,rg(r,t,void 0,e),e.currentTarget=null}function $f(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;Gu(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;Gu(i,a,u),s=l}}}if(Xi)throw e=na,Xi=!1,na=null,e}function Y(e,t){var n=t[fa];n===void 0&&(n=t[fa]=new Set);var r=e+"__bubble";n.has(r)||(Wf(t,e,2,!1),n.add(r))}function ho(e,t,n){var r=0;t&&(r|=4),Wf(n,e,r,t)}var Si="_reactListening"+Math.random().toString(36).slice(2);function br(e){if(!e[Si]){e[Si]=!0,Yd.forEach(function(n){n!=="selectionchange"&&(s0.has(n)||ho(n,!1,e),ho(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Si]||(t[Si]=!0,ho("selectionchange",!1,t))}}function Wf(e,t,n,r){switch(Ef(t)){case 1:var i=xg;break;case 4:i=wg;break;default:i=ul}n=i.bind(null,t,n,e),i=void 0,!ta||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function po(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=un(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}ff(function(){var u=s,c=sl(n),d=[];e:{var f=Uf.get(e);if(f!==void 0){var g=dl,v=e;switch(e){case"keypress":if(Fi(n)===0)break e;case"keydown":case"keyup":g=_g;break;case"focusin":v="focus",g=oo;break;case"focusout":v="blur",g=oo;break;case"beforeblur":case"afterblur":g=oo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=_u;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=kg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Fg;break;case Of:case Ff:case zf:g=Eg;break;case Bf:g=Bg;break;case"scroll":g=Sg;break;case"wheel":g=$g;break;case"copy":case"cut":case"paste":g=Ng;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Ou}var x=(t&4)!==0,P=!x&&e==="scroll",y=x?f!==null?f+"Capture":null:f;x=[];for(var p=u,m;p!==null;){m=p;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,y!==null&&(w=Fr(p,y),w!=null&&x.push(Hr(p,w,m)))),P)break;p=p.return}0<x.length&&(f=new g(f,v,null,n,c),d.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==qo&&(v=n.relatedTarget||n.fromElement)&&(un(v)||v[Ct]))break e;if((g||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?un(v):null,v!==null&&(P=Cn(v),v!==P||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(x=_u,w="onMouseLeave",y="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(x=Ou,w="onPointerLeave",y="onPointerEnter",p="pointer"),P=g==null?f:Mn(g),m=v==null?f:Mn(v),f=new x(w,p+"leave",g,n,c),f.target=P,f.relatedTarget=m,w=null,un(c)===u&&(x=new x(y,p+"enter",v,n,c),x.target=m,x.relatedTarget=P,w=x),P=w,g&&v)t:{for(x=g,y=v,p=0,m=x;m;m=En(m))p++;for(m=0,w=y;w;w=En(w))m++;for(;0<p-m;)x=En(x),p--;for(;0<m-p;)y=En(y),m--;for(;p--;){if(x===y||y!==null&&x===y.alternate)break t;x=En(x),y=En(y)}x=null}else x=null;g!==null&&Ku(d,f,g,x,!1),v!==null&&P!==null&&Ku(d,P,v,x,!0)}}e:{if(f=u?Mn(u):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var C=Yg;else if(Bu(f))if(Mf)C=qg;else{C=Zg;var E=Xg}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(C=Jg);if(C&&(C=C(e,u))){Rf(d,C,n,c);break e}E&&E(e,f,u),e==="focusout"&&(E=f._wrapperState)&&E.controlled&&f.type==="number"&&Ko(f,"number",f.value)}switch(E=u?Mn(u):window,e){case"focusin":(Bu(E)||E.contentEditable==="true")&&(An=E,oa=u,jr=null);break;case"focusout":jr=oa=An=null;break;case"mousedown":aa=!0;break;case"contextmenu":case"mouseup":case"dragend":aa=!1,Hu(d,n,c);break;case"selectionchange":if(n0)break;case"keydown":case"keyup":Hu(d,n,c)}var j;if(hl)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Ln?Lf(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(Nf&&n.locale!=="ko"&&(Ln||k!=="onCompositionStart"?k==="onCompositionEnd"&&Ln&&(j=jf()):(_t=c,cl="value"in _t?_t.value:_t.textContent,Ln=!0)),E=ts(u,k),0<E.length&&(k=new Iu(k,e,null,n,c),d.push({event:k,listeners:E}),j?k.data=j:(j=Af(n),j!==null&&(k.data=j)))),(j=bg?Hg(e,n):Qg(e,n))&&(u=ts(u,"onBeforeInput"),0<u.length&&(c=new Iu("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=j))}$f(d,t)})}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ts(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Fr(e,n),s!=null&&r.unshift(Hr(e,s,i)),s=Fr(e,t),s!=null&&r.push(Hr(e,s,i))),e=e.return}return r}function En(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ku(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Fr(n,s),l!=null&&o.unshift(Hr(n,l,a))):i||(l=Fr(n,s),l!=null&&o.push(Hr(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var o0=/\r\n?/g,a0=/\u0000|\uFFFD/g;function Yu(e){return(typeof e=="string"?e:""+e).replace(o0,`
`).replace(a0,"")}function Ci(e,t,n){if(t=Yu(t),Yu(e)!==t&&n)throw Error(N(425))}function ns(){}var la=null,ua=null;function ca(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var da=typeof setTimeout=="function"?setTimeout:void 0,l0=typeof clearTimeout=="function"?clearTimeout:void 0,Xu=typeof Promise=="function"?Promise:void 0,u0=typeof queueMicrotask=="function"?queueMicrotask:typeof Xu<"u"?function(e){return Xu.resolve(null).then(e).catch(c0)}:da;function c0(e){setTimeout(function(){throw e})}function mo(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Ur(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Ur(t)}function Bt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Zu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ar=Math.random().toString(36).slice(2),lt="__reactFiber$"+ar,Qr="__reactProps$"+ar,Ct="__reactContainer$"+ar,fa="__reactEvents$"+ar,d0="__reactListeners$"+ar,f0="__reactHandles$"+ar;function un(e){var t=e[lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ct]||n[lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Zu(e);e!==null;){if(n=e[lt])return n;e=Zu(e)}return t}e=n,n=e.parentNode}return null}function si(e){return e=e[lt]||e[Ct],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Mn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function Ms(e){return e[Qr]||null}var ha=[],Vn=-1;function Zt(e){return{current:e}}function X(e){0>Vn||(e.current=ha[Vn],ha[Vn]=null,Vn--)}function Q(e,t){Vn++,ha[Vn]=e.current,e.current=t}var Gt={},ke=Zt(Gt),Me=Zt(!1),gn=Gt;function Xn(e,t){var n=e.type.contextTypes;if(!n)return Gt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ve(e){return e=e.childContextTypes,e!=null}function rs(){X(Me),X(ke)}function Ju(e,t,n){if(ke.current!==Gt)throw Error(N(168));Q(ke,t),Q(Me,n)}function bf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(N(108,Xm(e)||"Unknown",i));return ne({},n,r)}function is(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gt,gn=ke.current,Q(ke,e),Q(Me,Me.current),!0}function qu(e,t,n){var r=e.stateNode;if(!r)throw Error(N(169));n?(e=bf(e,t,gn),r.__reactInternalMemoizedMergedChildContext=e,X(Me),X(ke),Q(ke,e)):X(Me),Q(Me,n)}var ht=null,Vs=!1,go=!1;function Hf(e){ht===null?ht=[e]:ht.push(e)}function h0(e){Vs=!0,Hf(e)}function Jt(){if(!go&&ht!==null){go=!0;var e=0,t=W;try{var n=ht;for(W=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}ht=null,Vs=!1}catch(i){throw ht!==null&&(ht=ht.slice(e+1)),gf(ol,Jt),i}finally{W=t,go=!1}}return null}var Dn=[],_n=0,ss=null,os=0,Qe=[],Ge=0,yn=null,pt=1,mt="";function rn(e,t){Dn[_n++]=os,Dn[_n++]=ss,ss=e,os=t}function Qf(e,t,n){Qe[Ge++]=pt,Qe[Ge++]=mt,Qe[Ge++]=yn,yn=e;var r=pt;e=mt;var i=32-nt(r)-1;r&=~(1<<i),n+=1;var s=32-nt(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,pt=1<<32-nt(t)+i|n<<i|r,mt=s+e}else pt=1<<s|n<<i|r,mt=e}function ml(e){e.return!==null&&(rn(e,1),Qf(e,1,0))}function gl(e){for(;e===ss;)ss=Dn[--_n],Dn[_n]=null,os=Dn[--_n],Dn[_n]=null;for(;e===yn;)yn=Qe[--Ge],Qe[Ge]=null,mt=Qe[--Ge],Qe[Ge]=null,pt=Qe[--Ge],Qe[Ge]=null}var ze=null,Fe=null,Z=!1,tt=null;function Gf(e,t){var n=Ke(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ec(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ze=e,Fe=Bt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ze=e,Fe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=yn!==null?{id:pt,overflow:mt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ke(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ze=e,Fe=null,!0):!1;default:return!1}}function pa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ma(e){if(Z){var t=Fe;if(t){var n=t;if(!ec(e,t)){if(pa(e))throw Error(N(418));t=Bt(n.nextSibling);var r=ze;t&&ec(e,t)?Gf(r,n):(e.flags=e.flags&-4097|2,Z=!1,ze=e)}}else{if(pa(e))throw Error(N(418));e.flags=e.flags&-4097|2,Z=!1,ze=e}}}function tc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ze=e}function ki(e){if(e!==ze)return!1;if(!Z)return tc(e),Z=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ca(e.type,e.memoizedProps)),t&&(t=Fe)){if(pa(e))throw Kf(),Error(N(418));for(;t;)Gf(e,t),t=Bt(t.nextSibling)}if(tc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Fe=Bt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Fe=null}}else Fe=ze?Bt(e.stateNode.nextSibling):null;return!0}function Kf(){for(var e=Fe;e;)e=Bt(e.nextSibling)}function Zn(){Fe=ze=null,Z=!1}function yl(e){tt===null?tt=[e]:tt.push(e)}var p0=Et.ReactCurrentBatchConfig;function hr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var r=n.stateNode}if(!r)throw Error(N(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,e))}return e}function Pi(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nc(e){var t=e._init;return t(e._payload)}function Yf(e){function t(y,p){if(e){var m=y.deletions;m===null?(y.deletions=[p],y.flags|=16):m.push(p)}}function n(y,p){if(!e)return null;for(;p!==null;)t(y,p),p=p.sibling;return null}function r(y,p){for(y=new Map;p!==null;)p.key!==null?y.set(p.key,p):y.set(p.index,p),p=p.sibling;return y}function i(y,p){return y=bt(y,p),y.index=0,y.sibling=null,y}function s(y,p,m){return y.index=m,e?(m=y.alternate,m!==null?(m=m.index,m<p?(y.flags|=2,p):m):(y.flags|=2,p)):(y.flags|=1048576,p)}function o(y){return e&&y.alternate===null&&(y.flags|=2),y}function a(y,p,m,w){return p===null||p.tag!==6?(p=ko(m,y.mode,w),p.return=y,p):(p=i(p,m),p.return=y,p)}function l(y,p,m,w){var C=m.type;return C===Nn?c(y,p,m.props.children,w,m.key):p!==null&&(p.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===At&&nc(C)===p.type)?(w=i(p,m.props),w.ref=hr(y,p,m),w.return=y,w):(w=Hi(m.type,m.key,m.props,null,y.mode,w),w.ref=hr(y,p,m),w.return=y,w)}function u(y,p,m,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==m.containerInfo||p.stateNode.implementation!==m.implementation?(p=Po(m,y.mode,w),p.return=y,p):(p=i(p,m.children||[]),p.return=y,p)}function c(y,p,m,w,C){return p===null||p.tag!==7?(p=pn(m,y.mode,w,C),p.return=y,p):(p=i(p,m),p.return=y,p)}function d(y,p,m){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ko(""+p,y.mode,m),p.return=y,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case hi:return m=Hi(p.type,p.key,p.props,null,y.mode,m),m.ref=hr(y,null,p),m.return=y,m;case jn:return p=Po(p,y.mode,m),p.return=y,p;case At:var w=p._init;return d(y,w(p._payload),m)}if(xr(p)||lr(p))return p=pn(p,y.mode,m,null),p.return=y,p;Pi(y,p)}return null}function f(y,p,m,w){var C=p!==null?p.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return C!==null?null:a(y,p,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case hi:return m.key===C?l(y,p,m,w):null;case jn:return m.key===C?u(y,p,m,w):null;case At:return C=m._init,f(y,p,C(m._payload),w)}if(xr(m)||lr(m))return C!==null?null:c(y,p,m,w,null);Pi(y,m)}return null}function g(y,p,m,w,C){if(typeof w=="string"&&w!==""||typeof w=="number")return y=y.get(m)||null,a(p,y,""+w,C);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case hi:return y=y.get(w.key===null?m:w.key)||null,l(p,y,w,C);case jn:return y=y.get(w.key===null?m:w.key)||null,u(p,y,w,C);case At:var E=w._init;return g(y,p,m,E(w._payload),C)}if(xr(w)||lr(w))return y=y.get(m)||null,c(p,y,w,C,null);Pi(p,w)}return null}function v(y,p,m,w){for(var C=null,E=null,j=p,k=p=0,A=null;j!==null&&k<m.length;k++){j.index>k?(A=j,j=null):A=j.sibling;var V=f(y,j,m[k],w);if(V===null){j===null&&(j=A);break}e&&j&&V.alternate===null&&t(y,j),p=s(V,p,k),E===null?C=V:E.sibling=V,E=V,j=A}if(k===m.length)return n(y,j),Z&&rn(y,k),C;if(j===null){for(;k<m.length;k++)j=d(y,m[k],w),j!==null&&(p=s(j,p,k),E===null?C=j:E.sibling=j,E=j);return Z&&rn(y,k),C}for(j=r(y,j);k<m.length;k++)A=g(j,y,k,m[k],w),A!==null&&(e&&A.alternate!==null&&j.delete(A.key===null?k:A.key),p=s(A,p,k),E===null?C=A:E.sibling=A,E=A);return e&&j.forEach(function(b){return t(y,b)}),Z&&rn(y,k),C}function x(y,p,m,w){var C=lr(m);if(typeof C!="function")throw Error(N(150));if(m=C.call(m),m==null)throw Error(N(151));for(var E=C=null,j=p,k=p=0,A=null,V=m.next();j!==null&&!V.done;k++,V=m.next()){j.index>k?(A=j,j=null):A=j.sibling;var b=f(y,j,V.value,w);if(b===null){j===null&&(j=A);break}e&&j&&b.alternate===null&&t(y,j),p=s(b,p,k),E===null?C=b:E.sibling=b,E=b,j=A}if(V.done)return n(y,j),Z&&rn(y,k),C;if(j===null){for(;!V.done;k++,V=m.next())V=d(y,V.value,w),V!==null&&(p=s(V,p,k),E===null?C=V:E.sibling=V,E=V);return Z&&rn(y,k),C}for(j=r(y,j);!V.done;k++,V=m.next())V=g(j,y,k,V.value,w),V!==null&&(e&&V.alternate!==null&&j.delete(V.key===null?k:V.key),p=s(V,p,k),E===null?C=V:E.sibling=V,E=V);return e&&j.forEach(function(F){return t(y,F)}),Z&&rn(y,k),C}function P(y,p,m,w){if(typeof m=="object"&&m!==null&&m.type===Nn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case hi:e:{for(var C=m.key,E=p;E!==null;){if(E.key===C){if(C=m.type,C===Nn){if(E.tag===7){n(y,E.sibling),p=i(E,m.props.children),p.return=y,y=p;break e}}else if(E.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===At&&nc(C)===E.type){n(y,E.sibling),p=i(E,m.props),p.ref=hr(y,E,m),p.return=y,y=p;break e}n(y,E);break}else t(y,E);E=E.sibling}m.type===Nn?(p=pn(m.props.children,y.mode,w,m.key),p.return=y,y=p):(w=Hi(m.type,m.key,m.props,null,y.mode,w),w.ref=hr(y,p,m),w.return=y,y=w)}return o(y);case jn:e:{for(E=m.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===m.containerInfo&&p.stateNode.implementation===m.implementation){n(y,p.sibling),p=i(p,m.children||[]),p.return=y,y=p;break e}else{n(y,p);break}else t(y,p);p=p.sibling}p=Po(m,y.mode,w),p.return=y,y=p}return o(y);case At:return E=m._init,P(y,p,E(m._payload),w)}if(xr(m))return v(y,p,m,w);if(lr(m))return x(y,p,m,w);Pi(y,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,p!==null&&p.tag===6?(n(y,p.sibling),p=i(p,m),p.return=y,y=p):(n(y,p),p=ko(m,y.mode,w),p.return=y,y=p),o(y)):n(y,p)}return P}var Jn=Yf(!0),Xf=Yf(!1),as=Zt(null),ls=null,In=null,vl=null;function xl(){vl=In=ls=null}function wl(e){var t=as.current;X(as),e._currentValue=t}function ga(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Gn(e,t){ls=e,vl=In=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Re=!0),e.firstContext=null)}function Xe(e){var t=e._currentValue;if(vl!==e)if(e={context:e,memoizedValue:t,next:null},In===null){if(ls===null)throw Error(N(308));In=e,ls.dependencies={lanes:0,firstContext:e}}else In=In.next=e;return t}var cn=null;function Sl(e){cn===null?cn=[e]:cn.push(e)}function Zf(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Sl(t)):(n.next=i.next,i.next=n),t.interleaved=n,kt(e,r)}function kt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Rt=!1;function Cl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function yt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ut(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,kt(e,n)}return i=r.interleaved,i===null?(t.next=t,Sl(r)):(t.next=i.next,i.next=t),r.interleaved=t,kt(e,n)}function zi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,al(e,n)}}function rc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function us(e,t,n,r){var i=e.updateQueue;Rt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(s!==null){var d=i.baseState;o=0,c=u=l=null,a=s;do{var f=a.lane,g=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(f=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){d=v.call(g,d,f);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(g,d,f):v,f==null)break e;d=ne({},d,f);break e;case 2:Rt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else g={eventTime:g,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=g,l=d):c=c.next=g,o|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(c===null&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);xn|=o,e.lanes=o,e.memoizedState=d}}function ic(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(N(191,i));i.call(r)}}}var oi={},ct=Zt(oi),Gr=Zt(oi),Kr=Zt(oi);function dn(e){if(e===oi)throw Error(N(174));return e}function kl(e,t){switch(Q(Kr,t),Q(Gr,e),Q(ct,oi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Xo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Xo(t,e)}X(ct),Q(ct,t)}function qn(){X(ct),X(Gr),X(Kr)}function qf(e){dn(Kr.current);var t=dn(ct.current),n=Xo(t,e.type);t!==n&&(Q(Gr,e),Q(ct,n))}function Pl(e){Gr.current===e&&(X(ct),X(Gr))}var q=Zt(0);function cs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var yo=[];function Tl(){for(var e=0;e<yo.length;e++)yo[e]._workInProgressVersionPrimary=null;yo.length=0}var Bi=Et.ReactCurrentDispatcher,vo=Et.ReactCurrentBatchConfig,vn=0,te=null,ce=null,fe=null,ds=!1,Nr=!1,Yr=0,m0=0;function xe(){throw Error(N(321))}function El(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!st(e[n],t[n]))return!1;return!0}function jl(e,t,n,r,i,s){if(vn=s,te=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Bi.current=e===null||e.memoizedState===null?x0:w0,e=n(r,i),Nr){s=0;do{if(Nr=!1,Yr=0,25<=s)throw Error(N(301));s+=1,fe=ce=null,t.updateQueue=null,Bi.current=S0,e=n(r,i)}while(Nr)}if(Bi.current=fs,t=ce!==null&&ce.next!==null,vn=0,fe=ce=te=null,ds=!1,t)throw Error(N(300));return e}function Nl(){var e=Yr!==0;return Yr=0,e}function at(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return fe===null?te.memoizedState=fe=e:fe=fe.next=e,fe}function Ze(){if(ce===null){var e=te.alternate;e=e!==null?e.memoizedState:null}else e=ce.next;var t=fe===null?te.memoizedState:fe.next;if(t!==null)fe=t,ce=e;else{if(e===null)throw Error(N(310));ce=e,e={memoizedState:ce.memoizedState,baseState:ce.baseState,baseQueue:ce.baseQueue,queue:ce.queue,next:null},fe===null?te.memoizedState=fe=e:fe=fe.next=e}return fe}function Xr(e,t){return typeof t=="function"?t(e):t}function xo(e){var t=Ze(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=ce,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var c=u.lane;if((vn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,o=r):l=l.next=d,te.lanes|=c,xn|=c}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,st(r,t.memoizedState)||(Re=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,te.lanes|=s,xn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function wo(e){var t=Ze(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);st(s,t.memoizedState)||(Re=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function eh(){}function th(e,t){var n=te,r=Ze(),i=t(),s=!st(r.memoizedState,i);if(s&&(r.memoizedState=i,Re=!0),r=r.queue,Ll(ih.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||fe!==null&&fe.memoizedState.tag&1){if(n.flags|=2048,Zr(9,rh.bind(null,n,r,i,t),void 0,null),he===null)throw Error(N(349));vn&30||nh(n,t,i)}return i}function nh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=te.updateQueue,t===null?(t={lastEffect:null,stores:null},te.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function rh(e,t,n,r){t.value=n,t.getSnapshot=r,sh(t)&&oh(e)}function ih(e,t,n){return n(function(){sh(t)&&oh(e)})}function sh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!st(e,n)}catch{return!0}}function oh(e){var t=kt(e,1);t!==null&&rt(t,e,1,-1)}function sc(e){var t=at();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Xr,lastRenderedState:e},t.queue=e,e=e.dispatch=v0.bind(null,te,e),[t.memoizedState,e]}function Zr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=te.updateQueue,t===null?(t={lastEffect:null,stores:null},te.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ah(){return Ze().memoizedState}function Ui(e,t,n,r){var i=at();te.flags|=e,i.memoizedState=Zr(1|t,n,void 0,r===void 0?null:r)}function Ds(e,t,n,r){var i=Ze();r=r===void 0?null:r;var s=void 0;if(ce!==null){var o=ce.memoizedState;if(s=o.destroy,r!==null&&El(r,o.deps)){i.memoizedState=Zr(t,n,s,r);return}}te.flags|=e,i.memoizedState=Zr(1|t,n,s,r)}function oc(e,t){return Ui(8390656,8,e,t)}function Ll(e,t){return Ds(2048,8,e,t)}function lh(e,t){return Ds(4,2,e,t)}function uh(e,t){return Ds(4,4,e,t)}function ch(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function dh(e,t,n){return n=n!=null?n.concat([e]):null,Ds(4,4,ch.bind(null,t,e),n)}function Al(){}function fh(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&El(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function hh(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&El(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ph(e,t,n){return vn&21?(st(n,t)||(n=xf(),te.lanes|=n,xn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Re=!0),e.memoizedState=n)}function g0(e,t){var n=W;W=n!==0&&4>n?n:4,e(!0);var r=vo.transition;vo.transition={};try{e(!1),t()}finally{W=n,vo.transition=r}}function mh(){return Ze().memoizedState}function y0(e,t,n){var r=Wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},gh(e))yh(t,n);else if(n=Zf(e,t,n,r),n!==null){var i=Ee();rt(n,e,r,i),vh(n,t,r)}}function v0(e,t,n){var r=Wt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(gh(e))yh(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,st(a,o)){var l=t.interleaved;l===null?(i.next=i,Sl(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Zf(e,t,i,r),n!==null&&(i=Ee(),rt(n,e,r,i),vh(n,t,r))}}function gh(e){var t=e.alternate;return e===te||t!==null&&t===te}function yh(e,t){Nr=ds=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function vh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,al(e,n)}}var fs={readContext:Xe,useCallback:xe,useContext:xe,useEffect:xe,useImperativeHandle:xe,useInsertionEffect:xe,useLayoutEffect:xe,useMemo:xe,useReducer:xe,useRef:xe,useState:xe,useDebugValue:xe,useDeferredValue:xe,useTransition:xe,useMutableSource:xe,useSyncExternalStore:xe,useId:xe,unstable_isNewReconciler:!1},x0={readContext:Xe,useCallback:function(e,t){return at().memoizedState=[e,t===void 0?null:t],e},useContext:Xe,useEffect:oc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ui(4194308,4,ch.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ui(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ui(4,2,e,t)},useMemo:function(e,t){var n=at();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=at();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=y0.bind(null,te,e),[r.memoizedState,e]},useRef:function(e){var t=at();return e={current:e},t.memoizedState=e},useState:sc,useDebugValue:Al,useDeferredValue:function(e){return at().memoizedState=e},useTransition:function(){var e=sc(!1),t=e[0];return e=g0.bind(null,e[1]),at().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=te,i=at();if(Z){if(n===void 0)throw Error(N(407));n=n()}else{if(n=t(),he===null)throw Error(N(349));vn&30||nh(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,oc(ih.bind(null,r,s,e),[e]),r.flags|=2048,Zr(9,rh.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=at(),t=he.identifierPrefix;if(Z){var n=mt,r=pt;n=(r&~(1<<32-nt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Yr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=m0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},w0={readContext:Xe,useCallback:fh,useContext:Xe,useEffect:Ll,useImperativeHandle:dh,useInsertionEffect:lh,useLayoutEffect:uh,useMemo:hh,useReducer:xo,useRef:ah,useState:function(){return xo(Xr)},useDebugValue:Al,useDeferredValue:function(e){var t=Ze();return ph(t,ce.memoizedState,e)},useTransition:function(){var e=xo(Xr)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:eh,useSyncExternalStore:th,useId:mh,unstable_isNewReconciler:!1},S0={readContext:Xe,useCallback:fh,useContext:Xe,useEffect:Ll,useImperativeHandle:dh,useInsertionEffect:lh,useLayoutEffect:uh,useMemo:hh,useReducer:wo,useRef:ah,useState:function(){return wo(Xr)},useDebugValue:Al,useDeferredValue:function(e){var t=Ze();return ce===null?t.memoizedState=e:ph(t,ce.memoizedState,e)},useTransition:function(){var e=wo(Xr)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:eh,useSyncExternalStore:th,useId:mh,unstable_isNewReconciler:!1};function qe(e,t){if(e&&e.defaultProps){t=ne({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ya(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ne({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var _s={isMounted:function(e){return(e=e._reactInternals)?Cn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ee(),i=Wt(e),s=yt(r,i);s.payload=t,n!=null&&(s.callback=n),t=Ut(e,s,i),t!==null&&(rt(t,e,i,r),zi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ee(),i=Wt(e),s=yt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Ut(e,s,i),t!==null&&(rt(t,e,i,r),zi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ee(),r=Wt(e),i=yt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Ut(e,i,r),t!==null&&(rt(t,e,r,n),zi(t,e,r))}};function ac(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Wr(n,r)||!Wr(i,s):!0}function xh(e,t,n){var r=!1,i=Gt,s=t.contextType;return typeof s=="object"&&s!==null?s=Xe(s):(i=Ve(t)?gn:ke.current,r=t.contextTypes,s=(r=r!=null)?Xn(e,i):Gt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=_s,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function lc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&_s.enqueueReplaceState(t,t.state,null)}function va(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Cl(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Xe(s):(s=Ve(t)?gn:ke.current,i.context=Xn(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(ya(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&_s.enqueueReplaceState(i,i.state,null),us(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function er(e,t){try{var n="",r=t;do n+=Ym(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function So(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function xa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var C0=typeof WeakMap=="function"?WeakMap:Map;function wh(e,t,n){n=yt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ps||(ps=!0,La=r),xa(e,t)},n}function Sh(e,t,n){n=yt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){xa(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){xa(e,t),typeof r!="function"&&($t===null?$t=new Set([this]):$t.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function uc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new C0;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=I0.bind(null,e,t,n),t.then(e,e))}function cc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function dc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=yt(-1,1),t.tag=2,Ut(n,t,1))),n.lanes|=1),e)}var k0=Et.ReactCurrentOwner,Re=!1;function Te(e,t,n,r){t.child=e===null?Xf(t,null,n,r):Jn(t,e.child,n,r)}function fc(e,t,n,r,i){n=n.render;var s=t.ref;return Gn(t,i),r=jl(e,t,n,r,s,i),n=Nl(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Pt(e,t,i)):(Z&&n&&ml(t),t.flags|=1,Te(e,t,r,i),t.child)}function hc(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Fl(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Ch(e,t,s,r,i)):(e=Hi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Wr,n(o,r)&&e.ref===t.ref)return Pt(e,t,i)}return t.flags|=1,e=bt(s,r),e.ref=t.ref,e.return=t,t.child=e}function Ch(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Wr(s,r)&&e.ref===t.ref)if(Re=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Re=!0);else return t.lanes=e.lanes,Pt(e,t,i)}return wa(e,t,n,r,i)}function kh(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Q(Fn,Oe),Oe|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Q(Fn,Oe),Oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,Q(Fn,Oe),Oe|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,Q(Fn,Oe),Oe|=r;return Te(e,t,i,n),t.child}function Ph(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function wa(e,t,n,r,i){var s=Ve(n)?gn:ke.current;return s=Xn(t,s),Gn(t,i),n=jl(e,t,n,r,s,i),r=Nl(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Pt(e,t,i)):(Z&&r&&ml(t),t.flags|=1,Te(e,t,n,i),t.child)}function pc(e,t,n,r,i){if(Ve(n)){var s=!0;is(t)}else s=!1;if(Gn(t,i),t.stateNode===null)$i(e,t),xh(t,n,r),va(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Xe(u):(u=Ve(n)?gn:ke.current,u=Xn(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&lc(t,o,r,u),Rt=!1;var f=t.memoizedState;o.state=f,us(t,r,o,i),l=t.memoizedState,a!==r||f!==l||Me.current||Rt?(typeof c=="function"&&(ya(t,n,c,r),l=t.memoizedState),(a=Rt||ac(t,n,a,r,f,l,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Jf(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:qe(t.type,a),o.props=u,d=t.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=Xe(l):(l=Ve(n)?gn:ke.current,l=Xn(t,l));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==d||f!==l)&&lc(t,o,r,l),Rt=!1,f=t.memoizedState,o.state=f,us(t,r,o,i);var v=t.memoizedState;a!==d||f!==v||Me.current||Rt?(typeof g=="function"&&(ya(t,n,g,r),v=t.memoizedState),(u=Rt||ac(t,n,u,r,f,v,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Sa(e,t,n,r,s,i)}function Sa(e,t,n,r,i,s){Ph(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&qu(t,n,!1),Pt(e,t,s);r=t.stateNode,k0.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Jn(t,e.child,null,s),t.child=Jn(t,null,a,s)):Te(e,t,a,s),t.memoizedState=r.state,i&&qu(t,n,!0),t.child}function Th(e){var t=e.stateNode;t.pendingContext?Ju(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ju(e,t.context,!1),kl(e,t.containerInfo)}function mc(e,t,n,r,i){return Zn(),yl(i),t.flags|=256,Te(e,t,n,r),t.child}var Ca={dehydrated:null,treeContext:null,retryLane:0};function ka(e){return{baseLanes:e,cachePool:null,transitions:null}}function Eh(e,t,n){var r=t.pendingProps,i=q.current,s=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),Q(q,i&1),e===null)return ma(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Fs(o,r,0,null),e=pn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=ka(n),t.memoizedState=Ca,e):Rl(t,o));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return P0(e,t,o,r,a,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=bt(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=bt(a,s):(s=pn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?ka(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Ca,r}return s=e.child,e=s.sibling,r=bt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Rl(e,t){return t=Fs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ti(e,t,n,r){return r!==null&&yl(r),Jn(t,e.child,null,n),e=Rl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function P0(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=So(Error(N(422))),Ti(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Fs({mode:"visible",children:r.children},i,0,null),s=pn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Jn(t,e.child,null,o),t.child.memoizedState=ka(o),t.memoizedState=Ca,s);if(!(t.mode&1))return Ti(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(N(419)),r=So(s,r,void 0),Ti(e,t,o,r)}if(a=(o&e.childLanes)!==0,Re||a){if(r=he,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,kt(e,i),rt(r,e,i,-1))}return Ol(),r=So(Error(N(421))),Ti(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=O0.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Fe=Bt(i.nextSibling),ze=t,Z=!0,tt=null,e!==null&&(Qe[Ge++]=pt,Qe[Ge++]=mt,Qe[Ge++]=yn,pt=e.id,mt=e.overflow,yn=t),t=Rl(t,r.children),t.flags|=4096,t)}function gc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ga(e.return,t,n)}function Co(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function jh(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(Te(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gc(e,n,t);else if(e.tag===19)gc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Q(q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&cs(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Co(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&cs(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Co(t,!0,n,null,s);break;case"together":Co(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $i(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Pt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),xn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,n=bt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=bt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function T0(e,t,n){switch(t.tag){case 3:Th(t),Zn();break;case 5:qf(t);break;case 1:Ve(t.type)&&is(t);break;case 4:kl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;Q(as,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Q(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Eh(e,t,n):(Q(q,q.current&1),e=Pt(e,t,n),e!==null?e.sibling:null);Q(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return jh(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Q(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,kh(e,t,n)}return Pt(e,t,n)}var Nh,Pa,Lh,Ah;Nh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Pa=function(){};Lh=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,dn(ct.current);var s=null;switch(n){case"input":i=Qo(e,i),r=Qo(e,r),s=[];break;case"select":i=ne({},i,{value:void 0}),r=ne({},r,{value:void 0}),s=[];break;case"textarea":i=Yo(e,i),r=Yo(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ns)}Zo(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ir.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ir.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Y("scroll",e),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Ah=function(e,t,n,r){n!==r&&(t.flags|=4)};function pr(e,t){if(!Z)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function E0(e,t,n){var r=t.pendingProps;switch(gl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return Ve(t.type)&&rs(),we(t),null;case 3:return r=t.stateNode,qn(),X(Me),X(ke),Tl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ki(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,tt!==null&&(Ma(tt),tt=null))),Pa(e,t),we(t),null;case 5:Pl(t);var i=dn(Kr.current);if(n=t.type,e!==null&&t.stateNode!=null)Lh(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(N(166));return we(t),null}if(e=dn(ct.current),ki(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[lt]=t,r[Qr]=s,e=(t.mode&1)!==0,n){case"dialog":Y("cancel",r),Y("close",r);break;case"iframe":case"object":case"embed":Y("load",r);break;case"video":case"audio":for(i=0;i<Sr.length;i++)Y(Sr[i],r);break;case"source":Y("error",r);break;case"img":case"image":case"link":Y("error",r),Y("load",r);break;case"details":Y("toggle",r);break;case"input":Tu(r,s),Y("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},Y("invalid",r);break;case"textarea":ju(r,s),Y("invalid",r)}Zo(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Ci(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Ci(r.textContent,a,e),i=["children",""+a]):Ir.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&Y("scroll",r)}switch(n){case"input":pi(r),Eu(r,s,!0);break;case"textarea":pi(r),Nu(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ns)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=rf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[lt]=t,e[Qr]=r,Nh(e,t,!1,!1),t.stateNode=e;e:{switch(o=Jo(n,r),n){case"dialog":Y("cancel",e),Y("close",e),i=r;break;case"iframe":case"object":case"embed":Y("load",e),i=r;break;case"video":case"audio":for(i=0;i<Sr.length;i++)Y(Sr[i],e);i=r;break;case"source":Y("error",e),i=r;break;case"img":case"image":case"link":Y("error",e),Y("load",e),i=r;break;case"details":Y("toggle",e),i=r;break;case"input":Tu(e,r),i=Qo(e,r),Y("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=ne({},r,{value:void 0}),Y("invalid",e);break;case"textarea":ju(e,r),i=Yo(e,r),Y("invalid",e);break;default:i=r}Zo(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?af(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&sf(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Or(e,l):typeof l=="number"&&Or(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Ir.hasOwnProperty(s)?l!=null&&s==="onScroll"&&Y("scroll",e):l!=null&&tl(e,s,l,o))}switch(n){case"input":pi(e),Eu(e,r,!1);break;case"textarea":pi(e),Nu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Qt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Wn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ns)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return we(t),null;case 6:if(e&&t.stateNode!=null)Ah(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(N(166));if(n=dn(Kr.current),dn(ct.current),ki(t)){if(r=t.stateNode,n=t.memoizedProps,r[lt]=t,(s=r.nodeValue!==n)&&(e=ze,e!==null))switch(e.tag){case 3:Ci(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ci(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[lt]=t,t.stateNode=r}return we(t),null;case 13:if(X(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Z&&Fe!==null&&t.mode&1&&!(t.flags&128))Kf(),Zn(),t.flags|=98560,s=!1;else if(s=ki(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(N(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(N(317));s[lt]=t}else Zn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;we(t),s=!1}else tt!==null&&(Ma(tt),tt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?de===0&&(de=3):Ol())),t.updateQueue!==null&&(t.flags|=4),we(t),null);case 4:return qn(),Pa(e,t),e===null&&br(t.stateNode.containerInfo),we(t),null;case 10:return wl(t.type._context),we(t),null;case 17:return Ve(t.type)&&rs(),we(t),null;case 19:if(X(q),s=t.memoizedState,s===null)return we(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)pr(s,!1);else{if(de!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=cs(e),o!==null){for(t.flags|=128,pr(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Q(q,q.current&1|2),t.child}e=e.sibling}s.tail!==null&&se()>tr&&(t.flags|=128,r=!0,pr(s,!1),t.lanes=4194304)}else{if(!r)if(e=cs(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),pr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!Z)return we(t),null}else 2*se()-s.renderingStartTime>tr&&n!==1073741824&&(t.flags|=128,r=!0,pr(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=se(),t.sibling=null,n=q.current,Q(q,r?n&1|2:n&1),t):(we(t),null);case 22:case 23:return Il(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Oe&1073741824&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function j0(e,t){switch(gl(t),t.tag){case 1:return Ve(t.type)&&rs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return qn(),X(Me),X(ke),Tl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Pl(t),null;case 13:if(X(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));Zn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(q),null;case 4:return qn(),null;case 10:return wl(t.type._context),null;case 22:case 23:return Il(),null;case 24:return null;default:return null}}var Ei=!1,Ce=!1,N0=typeof WeakSet=="function"?WeakSet:Set,M=null;function On(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ie(e,t,r)}else n.current=null}function Ta(e,t,n){try{n()}catch(r){ie(e,t,r)}}var yc=!1;function L0(e,t){if(la=qi,e=_f(),pl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var g;d!==n||i!==0&&d.nodeType!==3||(a=o+i),d!==s||r!==0&&d.nodeType!==3||(l=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(g=d.firstChild)!==null;)f=d,d=g;for(;;){if(d===e)break t;if(f===n&&++u===i&&(a=o),f===s&&++c===r&&(l=o),(g=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=g}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ua={focusedElem:e,selectionRange:n},qi=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,P=v.memoizedState,y=t.stateNode,p=y.getSnapshotBeforeUpdate(t.elementType===t.type?x:qe(t.type,x),P);y.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(w){ie(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return v=yc,yc=!1,v}function Lr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Ta(t,n,s)}i=i.next}while(i!==r)}}function Is(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ea(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Rh(e){var t=e.alternate;t!==null&&(e.alternate=null,Rh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[lt],delete t[Qr],delete t[fa],delete t[d0],delete t[f0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Mh(e){return e.tag===5||e.tag===3||e.tag===4}function vc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Mh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ja(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ns));else if(r!==4&&(e=e.child,e!==null))for(ja(e,t,n),e=e.sibling;e!==null;)ja(e,t,n),e=e.sibling}function Na(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Na(e,t,n),e=e.sibling;e!==null;)Na(e,t,n),e=e.sibling}var me=null,et=!1;function Nt(e,t,n){for(n=n.child;n!==null;)Vh(e,t,n),n=n.sibling}function Vh(e,t,n){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(Ns,n)}catch{}switch(n.tag){case 5:Ce||On(n,t);case 6:var r=me,i=et;me=null,Nt(e,t,n),me=r,et=i,me!==null&&(et?(e=me,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):me.removeChild(n.stateNode));break;case 18:me!==null&&(et?(e=me,n=n.stateNode,e.nodeType===8?mo(e.parentNode,n):e.nodeType===1&&mo(e,n),Ur(e)):mo(me,n.stateNode));break;case 4:r=me,i=et,me=n.stateNode.containerInfo,et=!0,Nt(e,t,n),me=r,et=i;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&Ta(n,t,o),i=i.next}while(i!==r)}Nt(e,t,n);break;case 1:if(!Ce&&(On(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ie(n,t,a)}Nt(e,t,n);break;case 21:Nt(e,t,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,Nt(e,t,n),Ce=r):Nt(e,t,n);break;default:Nt(e,t,n)}}function xc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new N0),t.forEach(function(r){var i=F0.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Je(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:me=a.stateNode,et=!1;break e;case 3:me=a.stateNode.containerInfo,et=!0;break e;case 4:me=a.stateNode.containerInfo,et=!0;break e}a=a.return}if(me===null)throw Error(N(160));Vh(s,o,i),me=null,et=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ie(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Dh(t,e),t=t.sibling}function Dh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Je(t,e),ot(e),r&4){try{Lr(3,e,e.return),Is(3,e)}catch(x){ie(e,e.return,x)}try{Lr(5,e,e.return)}catch(x){ie(e,e.return,x)}}break;case 1:Je(t,e),ot(e),r&512&&n!==null&&On(n,n.return);break;case 5:if(Je(t,e),ot(e),r&512&&n!==null&&On(n,n.return),e.flags&32){var i=e.stateNode;try{Or(i,"")}catch(x){ie(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&tf(i,s),Jo(a,o);var u=Jo(a,s);for(o=0;o<l.length;o+=2){var c=l[o],d=l[o+1];c==="style"?af(i,d):c==="dangerouslySetInnerHTML"?sf(i,d):c==="children"?Or(i,d):tl(i,c,d,u)}switch(a){case"input":Go(i,s);break;case"textarea":nf(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?Wn(i,!!s.multiple,g,!1):f!==!!s.multiple&&(s.defaultValue!=null?Wn(i,!!s.multiple,s.defaultValue,!0):Wn(i,!!s.multiple,s.multiple?[]:"",!1))}i[Qr]=s}catch(x){ie(e,e.return,x)}}break;case 6:if(Je(t,e),ot(e),r&4){if(e.stateNode===null)throw Error(N(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){ie(e,e.return,x)}}break;case 3:if(Je(t,e),ot(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ur(t.containerInfo)}catch(x){ie(e,e.return,x)}break;case 4:Je(t,e),ot(e);break;case 13:Je(t,e),ot(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Dl=se())),r&4&&xc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Ce=(u=Ce)||c,Je(t,e),Ce=u):Je(t,e),ot(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(M=e,c=e.child;c!==null;){for(d=M=c;M!==null;){switch(f=M,g=f.child,f.tag){case 0:case 11:case 14:case 15:Lr(4,f,f.return);break;case 1:On(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){ie(r,n,x)}}break;case 5:On(f,f.return);break;case 22:if(f.memoizedState!==null){Sc(d);continue}}g!==null?(g.return=f,M=g):Sc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=d.stateNode,l=d.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=of("display",o))}catch(x){ie(e,e.return,x)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(x){ie(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Je(t,e),ot(e),r&4&&xc(e);break;case 21:break;default:Je(t,e),ot(e)}}function ot(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Mh(n)){var r=n;break e}n=n.return}throw Error(N(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Or(i,""),r.flags&=-33);var s=vc(e);Na(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=vc(e);ja(e,a,o);break;default:throw Error(N(161))}}catch(l){ie(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function A0(e,t,n){M=e,_h(e)}function _h(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Ei;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Ce;a=Ei;var u=Ce;if(Ei=o,(Ce=l)&&!u)for(M=i;M!==null;)o=M,l=o.child,o.tag===22&&o.memoizedState!==null?Cc(i):l!==null?(l.return=o,M=l):Cc(i);for(;s!==null;)M=s,_h(s),s=s.sibling;M=i,Ei=a,Ce=u}wc(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,M=s):wc(e)}}function wc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ce||Is(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:qe(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&ic(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ic(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Ur(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}Ce||t.flags&512&&Ea(t)}catch(f){ie(t,t.return,f)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Sc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Cc(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Is(4,t)}catch(l){ie(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ie(t,i,l)}}var s=t.return;try{Ea(t)}catch(l){ie(t,s,l)}break;case 5:var o=t.return;try{Ea(t)}catch(l){ie(t,o,l)}}}catch(l){ie(t,t.return,l)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var R0=Math.ceil,hs=Et.ReactCurrentDispatcher,Ml=Et.ReactCurrentOwner,Ye=Et.ReactCurrentBatchConfig,B=0,he=null,le=null,ge=0,Oe=0,Fn=Zt(0),de=0,Jr=null,xn=0,Os=0,Vl=0,Ar=null,Ae=null,Dl=0,tr=1/0,ft=null,ps=!1,La=null,$t=null,ji=!1,It=null,ms=0,Rr=0,Aa=null,Wi=-1,bi=0;function Ee(){return B&6?se():Wi!==-1?Wi:Wi=se()}function Wt(e){return e.mode&1?B&2&&ge!==0?ge&-ge:p0.transition!==null?(bi===0&&(bi=xf()),bi):(e=W,e!==0||(e=window.event,e=e===void 0?16:Ef(e.type)),e):1}function rt(e,t,n,r){if(50<Rr)throw Rr=0,Aa=null,Error(N(185));ri(e,n,r),(!(B&2)||e!==he)&&(e===he&&(!(B&2)&&(Os|=n),de===4&&Dt(e,ge)),De(e,r),n===1&&B===0&&!(t.mode&1)&&(tr=se()+500,Vs&&Jt()))}function De(e,t){var n=e.callbackNode;pg(e,t);var r=Ji(e,e===he?ge:0);if(r===0)n!==null&&Ru(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ru(n),t===1)e.tag===0?h0(kc.bind(null,e)):Hf(kc.bind(null,e)),u0(function(){!(B&6)&&Jt()}),n=null;else{switch(wf(r)){case 1:n=ol;break;case 4:n=yf;break;case 16:n=Zi;break;case 536870912:n=vf;break;default:n=Zi}n=Wh(n,Ih.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Ih(e,t){if(Wi=-1,bi=0,B&6)throw Error(N(327));var n=e.callbackNode;if(Kn()&&e.callbackNode!==n)return null;var r=Ji(e,e===he?ge:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=gs(e,r);else{t=r;var i=B;B|=2;var s=Fh();(he!==e||ge!==t)&&(ft=null,tr=se()+500,hn(e,t));do try{D0();break}catch(a){Oh(e,a)}while(!0);xl(),hs.current=s,B=i,le!==null?t=0:(he=null,ge=0,t=de)}if(t!==0){if(t===2&&(i=ra(e),i!==0&&(r=i,t=Ra(e,i))),t===1)throw n=Jr,hn(e,0),Dt(e,r),De(e,se()),n;if(t===6)Dt(e,r);else{if(i=e.current.alternate,!(r&30)&&!M0(i)&&(t=gs(e,r),t===2&&(s=ra(e),s!==0&&(r=s,t=Ra(e,s))),t===1))throw n=Jr,hn(e,0),Dt(e,r),De(e,se()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(N(345));case 2:sn(e,Ae,ft);break;case 3:if(Dt(e,r),(r&130023424)===r&&(t=Dl+500-se(),10<t)){if(Ji(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ee(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=da(sn.bind(null,e,Ae,ft),t);break}sn(e,Ae,ft);break;case 4:if(Dt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-nt(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*R0(r/1960))-r,10<r){e.timeoutHandle=da(sn.bind(null,e,Ae,ft),r);break}sn(e,Ae,ft);break;case 5:sn(e,Ae,ft);break;default:throw Error(N(329))}}}return De(e,se()),e.callbackNode===n?Ih.bind(null,e):null}function Ra(e,t){var n=Ar;return e.current.memoizedState.isDehydrated&&(hn(e,t).flags|=256),e=gs(e,t),e!==2&&(t=Ae,Ae=n,t!==null&&Ma(t)),e}function Ma(e){Ae===null?Ae=e:Ae.push.apply(Ae,e)}function M0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!st(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Dt(e,t){for(t&=~Vl,t&=~Os,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-nt(t),r=1<<n;e[n]=-1,t&=~r}}function kc(e){if(B&6)throw Error(N(327));Kn();var t=Ji(e,0);if(!(t&1))return De(e,se()),null;var n=gs(e,t);if(e.tag!==0&&n===2){var r=ra(e);r!==0&&(t=r,n=Ra(e,r))}if(n===1)throw n=Jr,hn(e,0),Dt(e,t),De(e,se()),n;if(n===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,sn(e,Ae,ft),De(e,se()),null}function _l(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(tr=se()+500,Vs&&Jt())}}function wn(e){It!==null&&It.tag===0&&!(B&6)&&Kn();var t=B;B|=1;var n=Ye.transition,r=W;try{if(Ye.transition=null,W=1,e)return e()}finally{W=r,Ye.transition=n,B=t,!(B&6)&&Jt()}}function Il(){Oe=Fn.current,X(Fn)}function hn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,l0(n)),le!==null)for(n=le.return;n!==null;){var r=n;switch(gl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&rs();break;case 3:qn(),X(Me),X(ke),Tl();break;case 5:Pl(r);break;case 4:qn();break;case 13:X(q);break;case 19:X(q);break;case 10:wl(r.type._context);break;case 22:case 23:Il()}n=n.return}if(he=e,le=e=bt(e.current,null),ge=Oe=t,de=0,Jr=null,Vl=Os=xn=0,Ae=Ar=null,cn!==null){for(t=0;t<cn.length;t++)if(n=cn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}cn=null}return e}function Oh(e,t){do{var n=le;try{if(xl(),Bi.current=fs,ds){for(var r=te.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ds=!1}if(vn=0,fe=ce=te=null,Nr=!1,Yr=0,Ml.current=null,n===null||n.return===null){de=1,Jr=t,le=null;break}e:{var s=e,o=n.return,a=n,l=t;if(t=ge,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=cc(o);if(g!==null){g.flags&=-257,dc(g,o,a,s,t),g.mode&1&&uc(s,u,t),t=g,l=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){uc(s,u,t),Ol();break e}l=Error(N(426))}}else if(Z&&a.mode&1){var P=cc(o);if(P!==null){!(P.flags&65536)&&(P.flags|=256),dc(P,o,a,s,t),yl(er(l,a));break e}}s=l=er(l,a),de!==4&&(de=2),Ar===null?Ar=[s]:Ar.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var y=wh(s,l,t);rc(s,y);break e;case 1:a=l;var p=s.type,m=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&($t===null||!$t.has(m)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=Sh(s,a,t);rc(s,w);break e}}s=s.return}while(s!==null)}Bh(n)}catch(C){t=C,le===n&&n!==null&&(le=n=n.return);continue}break}while(!0)}function Fh(){var e=hs.current;return hs.current=fs,e===null?fs:e}function Ol(){(de===0||de===3||de===2)&&(de=4),he===null||!(xn&268435455)&&!(Os&268435455)||Dt(he,ge)}function gs(e,t){var n=B;B|=2;var r=Fh();(he!==e||ge!==t)&&(ft=null,hn(e,t));do try{V0();break}catch(i){Oh(e,i)}while(!0);if(xl(),B=n,hs.current=r,le!==null)throw Error(N(261));return he=null,ge=0,de}function V0(){for(;le!==null;)zh(le)}function D0(){for(;le!==null&&!sg();)zh(le)}function zh(e){var t=$h(e.alternate,e,Oe);e.memoizedProps=e.pendingProps,t===null?Bh(e):le=t,Ml.current=null}function Bh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=j0(n,t),n!==null){n.flags&=32767,le=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{de=6,le=null;return}}else if(n=E0(n,t,Oe),n!==null){le=n;return}if(t=t.sibling,t!==null){le=t;return}le=t=e}while(t!==null);de===0&&(de=5)}function sn(e,t,n){var r=W,i=Ye.transition;try{Ye.transition=null,W=1,_0(e,t,n,r)}finally{Ye.transition=i,W=r}return null}function _0(e,t,n,r){do Kn();while(It!==null);if(B&6)throw Error(N(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(mg(e,s),e===he&&(le=he=null,ge=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ji||(ji=!0,Wh(Zi,function(){return Kn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Ye.transition,Ye.transition=null;var o=W;W=1;var a=B;B|=4,Ml.current=null,L0(e,n),Dh(n,e),t0(ua),qi=!!la,ua=la=null,e.current=n,A0(n),og(),B=a,W=o,Ye.transition=s}else e.current=n;if(ji&&(ji=!1,It=e,ms=i),s=e.pendingLanes,s===0&&($t=null),ug(n.stateNode),De(e,se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ps)throw ps=!1,e=La,La=null,e;return ms&1&&e.tag!==0&&Kn(),s=e.pendingLanes,s&1?e===Aa?Rr++:(Rr=0,Aa=e):Rr=0,Jt(),null}function Kn(){if(It!==null){var e=wf(ms),t=Ye.transition,n=W;try{if(Ye.transition=null,W=16>e?16:e,It===null)var r=!1;else{if(e=It,It=null,ms=0,B&6)throw Error(N(331));var i=B;for(B|=4,M=e.current;M!==null;){var s=M,o=s.child;if(M.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(M=u;M!==null;){var c=M;switch(c.tag){case 0:case 11:case 15:Lr(8,c,s)}var d=c.child;if(d!==null)d.return=c,M=d;else for(;M!==null;){c=M;var f=c.sibling,g=c.return;if(Rh(c),c===u){M=null;break}if(f!==null){f.return=g,M=f;break}M=g}}}var v=s.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var P=x.sibling;x.sibling=null,x=P}while(x!==null)}}M=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,M=o;else e:for(;M!==null;){if(s=M,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Lr(9,s,s.return)}var y=s.sibling;if(y!==null){y.return=s.return,M=y;break e}M=s.return}}var p=e.current;for(M=p;M!==null;){o=M;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,M=m;else e:for(o=p;M!==null;){if(a=M,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Is(9,a)}}catch(C){ie(a,a.return,C)}if(a===o){M=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,M=w;break e}M=a.return}}if(B=i,Jt(),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(Ns,e)}catch{}r=!0}return r}finally{W=n,Ye.transition=t}}return!1}function Pc(e,t,n){t=er(n,t),t=wh(e,t,1),e=Ut(e,t,1),t=Ee(),e!==null&&(ri(e,1,t),De(e,t))}function ie(e,t,n){if(e.tag===3)Pc(e,e,n);else for(;t!==null;){if(t.tag===3){Pc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&($t===null||!$t.has(r))){e=er(n,e),e=Sh(t,e,1),t=Ut(t,e,1),e=Ee(),t!==null&&(ri(t,1,e),De(t,e));break}}t=t.return}}function I0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ee(),e.pingedLanes|=e.suspendedLanes&n,he===e&&(ge&n)===n&&(de===4||de===3&&(ge&130023424)===ge&&500>se()-Dl?hn(e,0):Vl|=n),De(e,t)}function Uh(e,t){t===0&&(e.mode&1?(t=yi,yi<<=1,!(yi&130023424)&&(yi=4194304)):t=1);var n=Ee();e=kt(e,t),e!==null&&(ri(e,t,n),De(e,n))}function O0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Uh(e,n)}function F0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(N(314))}r!==null&&r.delete(t),Uh(e,n)}var $h;$h=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Me.current)Re=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Re=!1,T0(e,t,n);Re=!!(e.flags&131072)}else Re=!1,Z&&t.flags&1048576&&Qf(t,os,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$i(e,t),e=t.pendingProps;var i=Xn(t,ke.current);Gn(t,n),i=jl(null,t,r,e,i,n);var s=Nl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ve(r)?(s=!0,is(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Cl(t),i.updater=_s,t.stateNode=i,i._reactInternals=t,va(t,r,e,n),t=Sa(null,t,r,!0,s,n)):(t.tag=0,Z&&s&&ml(t),Te(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch($i(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=B0(r),e=qe(r,e),i){case 0:t=wa(null,t,r,e,n);break e;case 1:t=pc(null,t,r,e,n);break e;case 11:t=fc(null,t,r,e,n);break e;case 14:t=hc(null,t,r,qe(r.type,e),n);break e}throw Error(N(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),wa(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),pc(e,t,r,i,n);case 3:e:{if(Th(t),e===null)throw Error(N(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Jf(e,t),us(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=er(Error(N(423)),t),t=mc(e,t,r,n,i);break e}else if(r!==i){i=er(Error(N(424)),t),t=mc(e,t,r,n,i);break e}else for(Fe=Bt(t.stateNode.containerInfo.firstChild),ze=t,Z=!0,tt=null,n=Xf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Zn(),r===i){t=Pt(e,t,n);break e}Te(e,t,r,n)}t=t.child}return t;case 5:return qf(t),e===null&&ma(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,ca(r,i)?o=null:s!==null&&ca(r,s)&&(t.flags|=32),Ph(e,t),Te(e,t,o,n),t.child;case 6:return e===null&&ma(t),null;case 13:return Eh(e,t,n);case 4:return kl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Jn(t,null,r,n):Te(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),fc(e,t,r,i,n);case 7:return Te(e,t,t.pendingProps,n),t.child;case 8:return Te(e,t,t.pendingProps.children,n),t.child;case 12:return Te(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,Q(as,r._currentValue),r._currentValue=o,s!==null)if(st(s.value,o)){if(s.children===i.children&&!Me.current){t=Pt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=yt(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),ga(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(N(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),ga(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}Te(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Gn(t,n),i=Xe(i),r=r(i),t.flags|=1,Te(e,t,r,n),t.child;case 14:return r=t.type,i=qe(r,t.pendingProps),i=qe(r.type,i),hc(e,t,r,i,n);case 15:return Ch(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),$i(e,t),t.tag=1,Ve(r)?(e=!0,is(t)):e=!1,Gn(t,n),xh(t,r,i),va(t,r,i,n),Sa(null,t,r,!0,e,n);case 19:return jh(e,t,n);case 22:return kh(e,t,n)}throw Error(N(156,t.tag))};function Wh(e,t){return gf(e,t)}function z0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ke(e,t,n,r){return new z0(e,t,n,r)}function Fl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function B0(e){if(typeof e=="function")return Fl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===rl)return 11;if(e===il)return 14}return 2}function bt(e,t){var n=e.alternate;return n===null?(n=Ke(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Hi(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Fl(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Nn:return pn(n.children,i,s,t);case nl:o=8,i|=8;break;case $o:return e=Ke(12,n,t,i|2),e.elementType=$o,e.lanes=s,e;case Wo:return e=Ke(13,n,t,i),e.elementType=Wo,e.lanes=s,e;case bo:return e=Ke(19,n,t,i),e.elementType=bo,e.lanes=s,e;case Jd:return Fs(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Xd:o=10;break e;case Zd:o=9;break e;case rl:o=11;break e;case il:o=14;break e;case At:o=16,r=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=Ke(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function pn(e,t,n,r){return e=Ke(7,e,r,t),e.lanes=n,e}function Fs(e,t,n,r){return e=Ke(22,e,r,t),e.elementType=Jd,e.lanes=n,e.stateNode={isHidden:!1},e}function ko(e,t,n){return e=Ke(6,e,null,t),e.lanes=n,e}function Po(e,t,n){return t=Ke(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function U0(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ro(0),this.expirationTimes=ro(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ro(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function zl(e,t,n,r,i,s,o,a,l){return e=new U0(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Ke(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Cl(s),e}function $0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:jn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function bh(e){if(!e)return Gt;e=e._reactInternals;e:{if(Cn(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ve(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var n=e.type;if(Ve(n))return bf(e,n,t)}return t}function Hh(e,t,n,r,i,s,o,a,l){return e=zl(n,r,!0,e,i,s,o,a,l),e.context=bh(null),n=e.current,r=Ee(),i=Wt(n),s=yt(r,i),s.callback=t??null,Ut(n,s,i),e.current.lanes=i,ri(e,i,r),De(e,r),e}function zs(e,t,n,r){var i=t.current,s=Ee(),o=Wt(i);return n=bh(n),t.context===null?t.context=n:t.pendingContext=n,t=yt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ut(i,t,o),e!==null&&(rt(e,i,o,s),zi(e,i,o)),o}function ys(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bl(e,t){Tc(e,t),(e=e.alternate)&&Tc(e,t)}function W0(){return null}var Qh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ul(e){this._internalRoot=e}Bs.prototype.render=Ul.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));zs(e,t,null,null)};Bs.prototype.unmount=Ul.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wn(function(){zs(null,e,null,null)}),t[Ct]=null}};function Bs(e){this._internalRoot=e}Bs.prototype.unstable_scheduleHydration=function(e){if(e){var t=kf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Vt.length&&t!==0&&t<Vt[n].priority;n++);Vt.splice(n,0,e),n===0&&Tf(e)}};function $l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Us(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ec(){}function b0(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=ys(o);s.call(u)}}var o=Hh(t,r,e,0,null,!1,!1,"",Ec);return e._reactRootContainer=o,e[Ct]=o.current,br(e.nodeType===8?e.parentNode:e),wn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=ys(l);a.call(u)}}var l=zl(e,0,!1,null,null,!1,!1,"",Ec);return e._reactRootContainer=l,e[Ct]=l.current,br(e.nodeType===8?e.parentNode:e),wn(function(){zs(t,l,n,r)}),l}function $s(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=ys(o);a.call(l)}}zs(t,o,e,i)}else o=b0(n,t,e,i,r);return ys(o)}Sf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=wr(t.pendingLanes);n!==0&&(al(t,n|1),De(t,se()),!(B&6)&&(tr=se()+500,Jt()))}break;case 13:wn(function(){var r=kt(e,1);if(r!==null){var i=Ee();rt(r,e,1,i)}}),Bl(e,1)}};ll=function(e){if(e.tag===13){var t=kt(e,134217728);if(t!==null){var n=Ee();rt(t,e,134217728,n)}Bl(e,134217728)}};Cf=function(e){if(e.tag===13){var t=Wt(e),n=kt(e,t);if(n!==null){var r=Ee();rt(n,e,t,r)}Bl(e,t)}};kf=function(){return W};Pf=function(e,t){var n=W;try{return W=e,t()}finally{W=n}};ea=function(e,t,n){switch(t){case"input":if(Go(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ms(r);if(!i)throw Error(N(90));ef(r),Go(r,i)}}}break;case"textarea":nf(e,n);break;case"select":t=n.value,t!=null&&Wn(e,!!n.multiple,t,!1)}};cf=_l;df=wn;var H0={usingClientEntryPoint:!1,Events:[si,Mn,Ms,lf,uf,_l]},mr={findFiberByHostInstance:un,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Q0={bundleType:mr.bundleType,version:mr.version,rendererPackageName:mr.rendererPackageName,rendererConfig:mr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Et.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=pf(e),e===null?null:e.stateNode},findFiberByHostInstance:mr.findFiberByHostInstance||W0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ni.isDisabled&&Ni.supportsFiber)try{Ns=Ni.inject(Q0),ut=Ni}catch{}}$e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=H0;$e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!$l(t))throw Error(N(200));return $0(e,t,null,n)};$e.createRoot=function(e,t){if(!$l(e))throw Error(N(299));var n=!1,r="",i=Qh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=zl(e,1,!1,null,null,n,!1,r,i),e[Ct]=t.current,br(e.nodeType===8?e.parentNode:e),new Ul(t)};$e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=pf(t),e=e===null?null:e.stateNode,e};$e.flushSync=function(e){return wn(e)};$e.hydrate=function(e,t,n){if(!Us(t))throw Error(N(200));return $s(null,e,t,!0,n)};$e.hydrateRoot=function(e,t,n){if(!$l(e))throw Error(N(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=Qh;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Hh(t,null,e,1,n??null,i,!1,s,o),e[Ct]=t.current,br(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Bs(t)};$e.render=function(e,t,n){if(!Us(t))throw Error(N(200));return $s(null,e,t,!1,n)};$e.unmountComponentAtNode=function(e){if(!Us(e))throw Error(N(40));return e._reactRootContainer?(wn(function(){$s(null,null,e,!1,function(){e._reactRootContainer=null,e[Ct]=null})}),!0):!1};$e.unstable_batchedUpdates=_l;$e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Us(n))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return $s(e,t,n,!1,r)};$e.version="18.3.1-next-f1338f8080-20240426";function Gh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Gh)}catch(e){console.error(e)}}Gh(),Qd.exports=$e;var G0=Qd.exports,jc=G0;Bo.createRoot=jc.createRoot,Bo.hydrateRoot=jc.hydrateRoot;const Kh=S.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Ws=S.createContext({}),bs=S.createContext(null),Hs=typeof document<"u",Wl=Hs?S.useLayoutEffect:S.useEffect,Yh=S.createContext({strict:!1}),bl=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),K0="framerAppearId",Xh="data-"+bl(K0);function Y0(e,t,n,r){const{visualElement:i}=S.useContext(Ws),s=S.useContext(Yh),o=S.useContext(bs),a=S.useContext(Kh).reducedMotion,l=S.useRef();r=r||s.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;S.useInsertionEffect(()=>{u&&u.update(n,o)});const c=S.useRef(!!(n[Xh]&&!window.HandoffComplete));return Wl(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),S.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function zn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function X0(e,t,n){return S.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):zn(n)&&(n.current=r))},[t])}function qr(e){return typeof e=="string"||Array.isArray(e)}function Qs(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Hl=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ql=["initial",...Hl];function Gs(e){return Qs(e.animate)||Ql.some(t=>qr(e[t]))}function Zh(e){return!!(Gs(e)||e.variants)}function Z0(e,t){if(Gs(e)){const{initial:n,animate:r}=e;return{initial:n===!1||qr(n)?n:void 0,animate:qr(r)?r:void 0}}return e.inherit!==!1?t:{}}function J0(e){const{initial:t,animate:n}=Z0(e,S.useContext(Ws));return S.useMemo(()=>({initial:t,animate:n}),[Nc(t),Nc(n)])}function Nc(e){return Array.isArray(e)?e.join(" "):e}const Lc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ei={};for(const e in Lc)ei[e]={isEnabled:t=>Lc[e].some(n=>!!t[n])};function q0(e){for(const t in e)ei[t]={...ei[t],...e[t]}}const Gl=S.createContext({}),Jh=S.createContext({}),ey=Symbol.for("motionComponentSymbol");function ty({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&q0(e);function s(a,l){let u;const c={...S.useContext(Kh),...a,layoutId:ny(a)},{isStatic:d}=c,f=J0(a),g=r(a,d);if(!d&&Hs){f.visualElement=Y0(i,g,c,t);const v=S.useContext(Jh),x=S.useContext(Yh).strict;f.visualElement&&(u=f.visualElement.loadFeatures(c,x,e,v))}return S.createElement(Ws.Provider,{value:f},u&&f.visualElement?S.createElement(u,{visualElement:f.visualElement,...c}):null,n(i,a,X0(g,f.visualElement,l),g,d,f.visualElement))}const o=S.forwardRef(s);return o[ey]=i,o}function ny({layoutId:e}){const t=S.useContext(Gl).id;return t&&e!==void 0?t+"-"+e:e}function ry(e){function t(r,i={}){return ty(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const iy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Kl(e){return typeof e!="string"||e.includes("-")?!1:!!(iy.indexOf(e)>-1||/[A-Z]/.test(e))}const vs={};function sy(e){Object.assign(vs,e)}const ai=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],kn=new Set(ai);function qh(e,{layout:t,layoutId:n}){return kn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!vs[e]||e==="opacity")}const _e=e=>!!(e&&e.getVelocity),oy={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ay=ai.length;function ly(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let s="";for(let o=0;o<ay;o++){const a=ai[o];if(e[a]!==void 0){const l=oy[a]||a;s+=`${l}(${e[a]}) `}}return t&&!e.z&&(s+="translateZ(0)"),s=s.trim(),i?s=i(e,r?"":s):n&&r&&(s="none"),s}const ep=e=>t=>typeof t=="string"&&t.startsWith(e),tp=ep("--"),Va=ep("var(--"),uy=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,cy=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Kt=(e,t,n)=>Math.min(Math.max(n,e),t),Pn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Mr={...Pn,transform:e=>Kt(0,1,e)},Li={...Pn,default:1},Vr=e=>Math.round(e*1e5)/1e5,Ks=/(-)?([\d]*\.?[\d])+/g,np=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,dy=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function li(e){return typeof e=="string"}const ui=e=>({test:t=>li(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Lt=ui("deg"),dt=ui("%"),I=ui("px"),fy=ui("vh"),hy=ui("vw"),Ac={...dt,parse:e=>dt.parse(e)/100,transform:e=>dt.transform(e*100)},Rc={...Pn,transform:Math.round},rp={borderWidth:I,borderTopWidth:I,borderRightWidth:I,borderBottomWidth:I,borderLeftWidth:I,borderRadius:I,radius:I,borderTopLeftRadius:I,borderTopRightRadius:I,borderBottomRightRadius:I,borderBottomLeftRadius:I,width:I,maxWidth:I,height:I,maxHeight:I,size:I,top:I,right:I,bottom:I,left:I,padding:I,paddingTop:I,paddingRight:I,paddingBottom:I,paddingLeft:I,margin:I,marginTop:I,marginRight:I,marginBottom:I,marginLeft:I,rotate:Lt,rotateX:Lt,rotateY:Lt,rotateZ:Lt,scale:Li,scaleX:Li,scaleY:Li,scaleZ:Li,skew:Lt,skewX:Lt,skewY:Lt,distance:I,translateX:I,translateY:I,translateZ:I,x:I,y:I,z:I,perspective:I,transformPerspective:I,opacity:Mr,originX:Ac,originY:Ac,originZ:I,zIndex:Rc,fillOpacity:Mr,strokeOpacity:Mr,numOctaves:Rc};function Yl(e,t,n,r){const{style:i,vars:s,transform:o,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const d in t){const f=t[d];if(tp(d)){s[d]=f;continue}const g=rp[d],v=cy(f,g);if(kn.has(d)){if(l=!0,o[d]=v,!c)continue;f!==(g.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,a[d]=v):i[d]=v}if(t.transform||(l||r?i.transform=ly(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:d="50%",originY:f="50%",originZ:g=0}=a;i.transformOrigin=`${d} ${f} ${g}`}}const Xl=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ip(e,t,n){for(const r in t)!_e(t[r])&&!qh(r,n)&&(e[r]=t[r])}function py({transformTemplate:e},t,n){return S.useMemo(()=>{const r=Xl();return Yl(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function my(e,t,n){const r=e.style||{},i={};return ip(i,r,e),Object.assign(i,py(e,t,n)),e.transformValues?e.transformValues(i):i}function gy(e,t,n){const r={},i=my(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const yy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function xs(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||yy.has(e)}let sp=e=>!xs(e);function vy(e){e&&(sp=t=>t.startsWith("on")?!xs(t):e(t))}try{vy(require("@emotion/is-prop-valid").default)}catch{}function xy(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(sp(i)||n===!0&&xs(i)||!t&&!xs(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Mc(e,t,n){return typeof e=="string"?e:I.transform(t+n*e)}function wy(e,t,n){const r=Mc(t,e.x,e.width),i=Mc(n,e.y,e.height);return`${r} ${i}`}const Sy={offset:"stroke-dashoffset",array:"stroke-dasharray"},Cy={offset:"strokeDashoffset",array:"strokeDasharray"};function ky(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?Sy:Cy;e[s.offset]=I.transform(-r);const o=I.transform(t),a=I.transform(n);e[s.array]=`${o} ${a}`}function Zl(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,d,f){if(Yl(e,u,c,f),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:v,dimensions:x}=e;g.transform&&(x&&(v.transform=g.transform),delete g.transform),x&&(i!==void 0||s!==void 0||v.transform)&&(v.transformOrigin=wy(x,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),o!==void 0&&ky(g,o,a,l,!1)}const op=()=>({...Xl(),attrs:{}}),Jl=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Py(e,t,n,r){const i=S.useMemo(()=>{const s=op();return Zl(s,t,{enableHardwareAcceleration:!1},Jl(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};ip(s,e.style,e),i.style={...s,...i.style}}return i}function Ty(e=!1){return(n,r,i,{latestValues:s},o)=>{const l=(Kl(n)?Py:gy)(r,s,o,n),c={...xy(r,typeof n=="string",e),...l,ref:i},{children:d}=r,f=S.useMemo(()=>_e(d)?d.get():d,[d]);return S.createElement(n,{...c,children:f})}}function ap(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const lp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function up(e,t,n,r){ap(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(lp.has(i)?i:bl(i),t.attrs[i])}function ql(e,t){const{style:n}=e,r={};for(const i in n)(_e(n[i])||t.style&&_e(t.style[i])||qh(i,e))&&(r[i]=n[i]);return r}function cp(e,t){const n=ql(e,t);for(const r in e)if(_e(e[r])||_e(t[r])){const i=ai.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function eu(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function dp(e){const t=S.useRef(null);return t.current===null&&(t.current=e()),t.current}const ws=e=>Array.isArray(e),Ey=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),jy=e=>ws(e)?e[e.length-1]||0:e;function Qi(e){const t=_e(e)?e.get():e;return Ey(t)?t.toValue():t}function Ny({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,s){const o={latestValues:Ly(r,i,s,e),renderState:t()};return n&&(o.mount=a=>n(r,a,o)),o}const fp=e=>(t,n)=>{const r=S.useContext(Ws),i=S.useContext(bs),s=()=>Ny(e,t,r,i);return n?s():dp(s)};function Ly(e,t,n,r){const i={},s=r(e,{});for(const f in s)i[f]=Qi(s[f]);let{initial:o,animate:a}=e;const l=Gs(e),u=Zh(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const d=c?a:o;return d&&typeof d!="boolean"&&!Qs(d)&&(Array.isArray(d)?d:[d]).forEach(g=>{const v=eu(e,g);if(!v)return;const{transitionEnd:x,transition:P,...y}=v;for(const p in y){let m=y[p];if(Array.isArray(m)){const w=c?m.length-1:0;m=m[w]}m!==null&&(i[p]=m)}for(const p in x)i[p]=x[p]}),i}const oe=e=>e;class Vc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Ay(e){let t=new Vc,n=new Vc,r=0,i=!1,s=!1;const o=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const d=c&&i,f=d?t:n;return u&&o.add(l),f.add(l)&&d&&i&&(r=t.order.length),l},cancel:l=>{n.remove(l),o.delete(l)},process:l=>{if(i){s=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(l),o.has(c)&&(a.schedule(c),e())}i=!1,s&&(s=!1,a.process(l))}};return a}const Ai=["prepare","read","update","preRender","render","postRender"],Ry=40;function My(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=Ai.reduce((d,f)=>(d[f]=Ay(()=>n=!0),d),{}),o=d=>s[d].process(i),a=()=>{const d=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(d-i.timestamp,Ry),1),i.timestamp=d,i.isProcessing=!0,Ai.forEach(o),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:Ai.reduce((d,f)=>{const g=s[f];return d[f]=(v,x=!1,P=!1)=>(n||l(),g.schedule(v,x,P)),d},{}),cancel:d=>Ai.forEach(f=>s[f].cancel(d)),state:i,steps:s}}const{schedule:G,cancel:Tt,state:Se,steps:To}=My(typeof requestAnimationFrame<"u"?requestAnimationFrame:oe,!0),Vy={useVisualState:fp({scrapeMotionValuesFromProps:cp,createRenderState:op,onMount:(e,t,{renderState:n,latestValues:r})=>{G.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),G.render(()=>{Zl(n,r,{enableHardwareAcceleration:!1},Jl(t.tagName),e.transformTemplate),up(t,n)})}})},Dy={useVisualState:fp({scrapeMotionValuesFromProps:ql,createRenderState:Xl})};function _y(e,{forwardMotionProps:t=!1},n,r){return{...Kl(e)?Vy:Dy,preloadedFeatures:n,useRender:Ty(t),createVisualElement:r,Component:e}}function gt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const hp=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function Ys(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const Iy=e=>t=>hp(t)&&e(t,Ys(t));function vt(e,t,n,r){return gt(e,t,Iy(n),r)}const Oy=(e,t)=>n=>t(e(n)),Ht=(...e)=>e.reduce(Oy);function pp(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Dc=pp("dragHorizontal"),_c=pp("dragVertical");function mp(e){let t=!1;if(e==="y")t=_c();else if(e==="x")t=Dc();else{const n=Dc(),r=_c();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function gp(){const e=mp(!0);return e?(e(),!1):!0}class qt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Ic(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(s,o)=>{if(s.pointerType==="touch"||gp())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&G.update(()=>a[r](s,o))};return vt(e.current,n,i,{passive:!e.getProps()[r]})}class Fy extends qt{mount(){this.unmount=Ht(Ic(this.node,!0),Ic(this.node,!1))}unmount(){}}class zy extends qt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ht(gt(this.node.current,"focus",()=>this.onFocus()),gt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const yp=(e,t)=>t?e===t?!0:yp(e,t.parentElement):!1;function Eo(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Ys(n))}class By extends qt{constructor(){super(...arguments),this.removeStartListeners=oe,this.removeEndListeners=oe,this.removeAccessibleListeners=oe,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),s=vt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:d}=this.node.getProps();G.update(()=>{!d&&!yp(this.node.current,a.target)?c&&c(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),o=vt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ht(s,o),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=s=>{if(s.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||Eo("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&G.update(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=gt(this.node.current,"keyup",o),Eo("down",(a,l)=>{this.startPress(a,l)})},n=gt(this.node.current,"keydown",t),r=()=>{this.isPressing&&Eo("cancel",(s,o)=>this.cancelPress(s,o))},i=gt(this.node.current,"blur",r);this.removeAccessibleListeners=Ht(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&G.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!gp()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&G.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=vt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=gt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ht(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Da=new WeakMap,jo=new WeakMap,Uy=e=>{const t=Da.get(e.target);t&&t(e)},$y=e=>{e.forEach(Uy)};function Wy({root:e,...t}){const n=e||document;jo.has(n)||jo.set(n,{});const r=jo.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver($y,{root:e,...t})),r[i]}function by(e,t,n){const r=Wy(t);return Da.set(e,n),r.observe(e),()=>{Da.delete(e),r.unobserve(e)}}const Hy={some:0,all:1};class Qy extends qt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:Hy[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=u?c:d;f&&f(l)};return by(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Gy(t,n))&&this.startObserver()}unmount(){}}function Gy({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Ky={inView:{Feature:Qy},tap:{Feature:By},focus:{Feature:zy},hover:{Feature:Fy}};function vp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Yy(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function Xy(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Xs(e,t,n){const r=e.getProps();return eu(r,t,n!==void 0?n:r.custom,Yy(e),Xy(e))}let tu=oe;const mn=e=>e*1e3,xt=e=>e/1e3,Zy={current:!1},xp=e=>Array.isArray(e)&&typeof e[0]=="number";function wp(e){return!!(!e||typeof e=="string"&&Sp[e]||xp(e)||Array.isArray(e)&&e.every(wp))}const Cr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Sp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Cr([0,.65,.55,1]),circOut:Cr([.55,0,1,.45]),backIn:Cr([.31,.01,.66,-.59]),backOut:Cr([.33,1.53,.69,.99])};function Cp(e){if(e)return xp(e)?Cr(e):Array.isArray(e)?e.map(Cp):Sp[e]}function Jy(e,t,n,{delay:r=0,duration:i,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=Cp(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}function qy(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const kp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,ev=1e-7,tv=12;function nv(e,t,n,r,i){let s,o,a=0;do o=t+(n-t)/2,s=kp(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>ev&&++a<tv);return o}function ci(e,t,n,r){if(e===t&&n===r)return oe;const i=s=>nv(s,0,1,e,n);return s=>s===0||s===1?s:kp(i(s),t,r)}const rv=ci(.42,0,1,1),iv=ci(0,0,.58,1),Pp=ci(.42,0,.58,1),sv=e=>Array.isArray(e)&&typeof e[0]!="number",Tp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Ep=e=>t=>1-e(1-t),nu=e=>1-Math.sin(Math.acos(e)),jp=Ep(nu),ov=Tp(nu),Np=ci(.33,1.53,.69,.99),ru=Ep(Np),av=Tp(ru),lv=e=>(e*=2)<1?.5*ru(e):.5*(2-Math.pow(2,-10*(e-1))),uv={linear:oe,easeIn:rv,easeInOut:Pp,easeOut:iv,circIn:nu,circInOut:ov,circOut:jp,backIn:ru,backInOut:av,backOut:Np,anticipate:lv},Oc=e=>{if(Array.isArray(e)){tu(e.length===4);const[t,n,r,i]=e;return ci(t,n,r,i)}else if(typeof e=="string")return uv[e];return e},iu=(e,t)=>n=>!!(li(n)&&dy.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Lp=(e,t,n)=>r=>{if(!li(r))return r;const[i,s,o,a]=r.match(Ks);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},cv=e=>Kt(0,255,e),No={...Pn,transform:e=>Math.round(cv(e))},fn={test:iu("rgb","red"),parse:Lp("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+No.transform(e)+", "+No.transform(t)+", "+No.transform(n)+", "+Vr(Mr.transform(r))+")"};function dv(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const _a={test:iu("#"),parse:dv,transform:fn.transform},Bn={test:iu("hsl","hue"),parse:Lp("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+dt.transform(Vr(t))+", "+dt.transform(Vr(n))+", "+Vr(Mr.transform(r))+")"},Pe={test:e=>fn.test(e)||_a.test(e)||Bn.test(e),parse:e=>fn.test(e)?fn.parse(e):Bn.test(e)?Bn.parse(e):_a.parse(e),transform:e=>li(e)?e:e.hasOwnProperty("red")?fn.transform(e):Bn.transform(e)},ee=(e,t,n)=>-n*e+n*t+e;function Lo(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function fv({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=Lo(l,a,e+1/3),s=Lo(l,a,e),o=Lo(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}const Ao=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},hv=[_a,fn,Bn],pv=e=>hv.find(t=>t.test(e));function Fc(e){const t=pv(e);let n=t.parse(e);return t===Bn&&(n=fv(n)),n}const Ap=(e,t)=>{const n=Fc(e),r=Fc(t),i={...n};return s=>(i.red=Ao(n.red,r.red,s),i.green=Ao(n.green,r.green,s),i.blue=Ao(n.blue,r.blue,s),i.alpha=ee(n.alpha,r.alpha,s),fn.transform(i))};function mv(e){var t,n;return isNaN(e)&&li(e)&&(((t=e.match(Ks))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(np))===null||n===void 0?void 0:n.length)||0)>0}const Rp={regex:uy,countKey:"Vars",token:"${v}",parse:oe},Mp={regex:np,countKey:"Colors",token:"${c}",parse:Pe.parse},Vp={regex:Ks,countKey:"Numbers",token:"${n}",parse:Pn.parse};function Ro(e,{regex:t,countKey:n,token:r,parse:i}){const s=e.tokenised.match(t);s&&(e["num"+n]=s.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...s.map(i)))}function Ss(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Ro(n,Rp),Ro(n,Mp),Ro(n,Vp),n}function Dp(e){return Ss(e).values}function _p(e){const{values:t,numColors:n,numVars:r,tokenised:i}=Ss(e),s=t.length;return o=>{let a=i;for(let l=0;l<s;l++)l<r?a=a.replace(Rp.token,o[l]):l<r+n?a=a.replace(Mp.token,Pe.transform(o[l])):a=a.replace(Vp.token,Vr(o[l]));return a}}const gv=e=>typeof e=="number"?0:e;function yv(e){const t=Dp(e);return _p(e)(t.map(gv))}const Yt={test:mv,parse:Dp,createTransformer:_p,getAnimatableNone:yv},Ip=(e,t)=>n=>`${n>0?t:e}`;function Op(e,t){return typeof e=="number"?n=>ee(e,t,n):Pe.test(e)?Ap(e,t):e.startsWith("var(")?Ip(e,t):zp(e,t)}const Fp=(e,t)=>{const n=[...e],r=n.length,i=e.map((s,o)=>Op(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}},vv=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Op(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}},zp=(e,t)=>{const n=Yt.createTransformer(t),r=Ss(e),i=Ss(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ht(Fp(r.values,i.values),n):Ip(e,t)},ti=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},zc=(e,t)=>n=>ee(e,t,n);function xv(e){return typeof e=="number"?zc:typeof e=="string"?Pe.test(e)?Ap:zp:Array.isArray(e)?Fp:typeof e=="object"?vv:zc}function wv(e,t,n){const r=[],i=n||xv(e[0]),s=e.length-1;for(let o=0;o<s;o++){let a=i(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||oe:t;a=Ht(l,a)}r.push(a)}return r}function Bp(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(tu(s===t.length),s===1)return()=>t[0];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=wv(t,r,i),a=o.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const d=ti(e[c],e[c+1],u);return o[c](d)};return n?u=>l(Kt(e[0],e[s-1],u)):l}function Sv(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=ti(0,t,r);e.push(ee(n,1,i))}}function Cv(e){const t=[0];return Sv(t,e.length-1),t}function kv(e,t){return e.map(n=>n*t)}function Pv(e,t){return e.map(()=>t||Pp).splice(0,e.length-1)}function Cs({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=sv(r)?r.map(Oc):Oc(r),s={done:!1,value:t[0]},o=kv(n&&n.length===t.length?n:Cv(t),e),a=Bp(o,t,{ease:Array.isArray(i)?i:Pv(t,i)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}function Up(e,t){return t?e*(1e3/t):0}const Tv=5;function $p(e,t,n){const r=Math.max(t-Tv,0);return Up(n-e(r),t-r)}const Mo=.001,Ev=.01,jv=10,Nv=.05,Lv=1;function Av({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,s,o=1-t;o=Kt(Nv,Lv,o),e=Kt(Ev,jv,xt(e)),o<1?(i=u=>{const c=u*o,d=c*e,f=c-n,g=Ia(u,o),v=Math.exp(-d);return Mo-f/g*v},s=u=>{const d=u*o*e,f=d*n+n,g=Math.pow(o,2)*Math.pow(u,2)*e,v=Math.exp(-d),x=Ia(Math.pow(u,2),o);return(-i(u)+Mo>0?-1:1)*((f-g)*v)/x}):(i=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-Mo+c*d},s=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=Mv(i,s,a);if(e=mn(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const Rv=12;function Mv(e,t,n){let r=n;for(let i=1;i<Rv;i++)r=r-e(r)/t(r);return r}function Ia(e,t){return e*Math.sqrt(1-t*t)}const Vv=["duration","bounce"],Dv=["stiffness","damping","mass"];function Bc(e,t){return t.some(n=>e[n]!==void 0)}function _v(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Bc(e,Dv)&&Bc(e,Vv)){const n=Av(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Wp({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],s=e[e.length-1],o={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:c,velocity:d,isResolvedFromDuration:f}=_v({...r,velocity:-xt(r.velocity||0)}),g=d||0,v=l/(2*Math.sqrt(a*u)),x=s-i,P=xt(Math.sqrt(a/u)),y=Math.abs(x)<5;n||(n=y?.01:2),t||(t=y?.005:.5);let p;if(v<1){const m=Ia(P,v);p=w=>{const C=Math.exp(-v*P*w);return s-C*((g+v*P*x)/m*Math.sin(m*w)+x*Math.cos(m*w))}}else if(v===1)p=m=>s-Math.exp(-P*m)*(x+(g+P*x)*m);else{const m=P*Math.sqrt(v*v-1);p=w=>{const C=Math.exp(-v*P*w),E=Math.min(m*w,300);return s-C*((g+v*P*x)*Math.sinh(E)+m*x*Math.cosh(E))/m}}return{calculatedDuration:f&&c||null,next:m=>{const w=p(m);if(f)o.done=m>=c;else{let C=g;m!==0&&(v<1?C=$p(p,m,w):C=0);const E=Math.abs(C)<=n,j=Math.abs(s-w)<=t;o.done=E&&j}return o.value=o.done?s:w,o}}}function Uc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},g=k=>a!==void 0&&k<a||l!==void 0&&k>l,v=k=>a===void 0?l:l===void 0||Math.abs(a-k)<Math.abs(l-k)?a:l;let x=n*t;const P=d+x,y=o===void 0?P:o(P);y!==P&&(x=y-d);const p=k=>-x*Math.exp(-k/r),m=k=>y+p(k),w=k=>{const A=p(k),V=m(k);f.done=Math.abs(A)<=u,f.value=f.done?y:V};let C,E;const j=k=>{g(f.value)&&(C=k,E=Wp({keyframes:[f.value,v(f.value)],velocity:$p(m,k,f.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:k=>{let A=!1;return!E&&C===void 0&&(A=!0,w(k),j(k)),C!==void 0&&k>C?E.next(k-C):(!A&&w(k),f)}}}const Iv=e=>{const t=({timestamp:n})=>e(n);return{start:()=>G.update(t,!0),stop:()=>Tt(t),now:()=>Se.isProcessing?Se.timestamp:performance.now()}},$c=2e4;function Wc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<$c;)t+=n,r=e.next(t);return t>=$c?1/0:t}const Ov={decay:Uc,inertia:Uc,tween:Cs,keyframes:Cs,spring:Wp};function ks({autoplay:e=!0,delay:t=0,driver:n=Iv,keyframes:r,type:i="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...f}){let g=1,v=!1,x,P;const y=()=>{P=new Promise(L=>{x=L})};y();let p;const m=Ov[i]||Cs;let w;m!==Cs&&typeof r[0]!="number"&&(w=Bp([0,100],r,{clamp:!1}),r=[0,100]);const C=m({...f,keyframes:r});let E;a==="mirror"&&(E=m({...f,keyframes:[...r].reverse(),velocity:-(f.velocity||0)}));let j="idle",k=null,A=null,V=null;C.calculatedDuration===null&&s&&(C.calculatedDuration=Wc(C));const{calculatedDuration:b}=C;let F=1/0,J=1/0;b!==null&&(F=b+o,J=F*(s+1)-o);let K=0;const Le=L=>{if(A===null)return;g>0&&(A=Math.min(A,L)),g<0&&(A=Math.min(L-J/g,A)),k!==null?K=k:K=Math.round(L-A)*g;const D=K-t*(g>=0?1:-1),re=g>=0?D<0:D>J;K=Math.max(D,0),j==="finished"&&k===null&&(K=J);let ve=K,Tn=C;if(s){const Zs=Math.min(K,J)/F;let di=Math.floor(Zs),tn=Zs%1;!tn&&Zs>=1&&(tn=1),tn===1&&di--,di=Math.min(di,s+1),!!(di%2)&&(a==="reverse"?(tn=1-tn,o&&(tn-=o/F)):a==="mirror"&&(Tn=E)),ve=Kt(0,1,tn)*F}const Ie=re?{done:!1,value:r[0]}:Tn.next(ve);w&&(Ie.value=w(Ie.value));let{done:en}=Ie;!re&&b!==null&&(en=g>=0?K>=J:K<=0);const vm=k===null&&(j==="finished"||j==="running"&&en);return d&&d(Ie.value),vm&&T(),Ie},H=()=>{p&&p.stop(),p=void 0},ue=()=>{j="idle",H(),x(),y(),A=V=null},T=()=>{j="finished",c&&c(),H(),x()},R=()=>{if(v)return;p||(p=n(Le));const L=p.now();l&&l(),k!==null?A=L-k:(!A||j==="finished")&&(A=L),j==="finished"&&y(),V=A,k=null,j="running",p.start()};e&&R();const _={then(L,D){return P.then(L,D)},get time(){return xt(K)},set time(L){L=mn(L),K=L,k!==null||!p||g===0?k=L:A=p.now()-L/g},get duration(){const L=C.calculatedDuration===null?Wc(C):C.calculatedDuration;return xt(L)},get speed(){return g},set speed(L){L===g||!p||(g=L,_.time=xt(K))},get state(){return j},play:R,pause:()=>{j="paused",k=K},stop:()=>{v=!0,j!=="idle"&&(j="idle",u&&u(),ue())},cancel:()=>{V!==null&&Le(V),ue()},complete:()=>{j="finished"},sample:L=>(A=0,Le(L))};return _}function Fv(e){let t;return()=>(t===void 0&&(t=e()),t)}const zv=Fv(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Bv=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ri=10,Uv=2e4,$v=(e,t)=>t.type==="spring"||e==="backgroundColor"||!wp(t.ease);function Wv(e,t,{onUpdate:n,onComplete:r,...i}){if(!(zv()&&Bv.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let o=!1,a,l,u=!1;const c=()=>{l=new Promise(m=>{a=m})};c();let{keyframes:d,duration:f=300,ease:g,times:v}=i;if($v(t,i)){const m=ks({...i,repeat:0,delay:0});let w={done:!1,value:d[0]};const C=[];let E=0;for(;!w.done&&E<Uv;)w=m.sample(E),C.push(w.value),E+=Ri;v=void 0,d=C,f=E-Ri,g="linear"}const x=Jy(e.owner.current,t,d,{...i,duration:f,ease:g,times:v}),P=()=>{u=!1,x.cancel()},y=()=>{u=!0,G.update(P),a(),c()};return x.onfinish=()=>{u||(e.set(qy(d,i)),r&&r(),y())},{then(m,w){return l.then(m,w)},attachTimeline(m){return x.timeline=m,x.onfinish=null,oe},get time(){return xt(x.currentTime||0)},set time(m){x.currentTime=mn(m)},get speed(){return x.playbackRate},set speed(m){x.playbackRate=m},get duration(){return xt(f)},play:()=>{o||(x.play(),Tt(P))},pause:()=>x.pause(),stop:()=>{if(o=!0,x.playState==="idle")return;const{currentTime:m}=x;if(m){const w=ks({...i,autoplay:!1});e.setWithVelocity(w.sample(m-Ri).value,w.sample(m).value,Ri)}y()},complete:()=>{u||x.finish()},cancel:y}}function bv({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:oe,pause:oe,stop:oe,then:s=>(s(),Promise.resolve()),cancel:oe,complete:oe});return t?ks({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const Hv={type:"spring",stiffness:500,damping:25,restSpeed:10},Qv=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Gv={type:"keyframes",duration:.8},Kv={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Yv=(e,{keyframes:t})=>t.length>2?Gv:kn.has(e)?e.startsWith("scale")?Qv(t[1]):Hv:Kv,Oa=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Yt.test(t)||t==="0")&&!t.startsWith("url(")),Xv=new Set(["brightness","contrast","saturate","opacity"]);function Zv(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Ks)||[];if(!r)return e;const i=n.replace(r,"");let s=Xv.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const Jv=/([a-z-]*)\(.*?\)/g,Fa={...Yt,getAnimatableNone:e=>{const t=e.match(Jv);return t?t.map(Zv).join(" "):e}},qv={...rp,color:Pe,backgroundColor:Pe,outlineColor:Pe,fill:Pe,stroke:Pe,borderColor:Pe,borderTopColor:Pe,borderRightColor:Pe,borderBottomColor:Pe,borderLeftColor:Pe,filter:Fa,WebkitFilter:Fa},su=e=>qv[e];function bp(e,t){let n=su(e);return n!==Fa&&(n=Yt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Hp=e=>/^0[^.\s]+$/.test(e);function ex(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Hp(e)}function tx(e,t,n,r){const i=Oa(t,n);let s;Array.isArray(n)?s=[...n]:s=[null,n];const o=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<s.length;u++)s[u]===null&&(s[u]=u===0?o:s[u-1]),ex(s[u])&&l.push(u),typeof s[u]=="string"&&s[u]!=="none"&&s[u]!=="0"&&(a=s[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++){const c=l[u];s[c]=bp(t,a)}return s}function nx({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function ou(e,t){return e[t]||e.default||e}const rx={skipAnimations:!1},au=(e,t,n,r={})=>i=>{const s=ou(r,e)||{},o=s.delay||r.delay||0;let{elapsed:a=0}=r;a=a-mn(o);const l=tx(t,e,n,s),u=l[0],c=l[l.length-1],d=Oa(e,u),f=Oa(e,c);let g={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:v=>{t.set(v),s.onUpdate&&s.onUpdate(v)},onComplete:()=>{i(),s.onComplete&&s.onComplete()}};if(nx(s)||(g={...g,...Yv(e,g)}),g.duration&&(g.duration=mn(g.duration)),g.repeatDelay&&(g.repeatDelay=mn(g.repeatDelay)),!d||!f||Zy.current||s.type===!1||rx.skipAnimations)return bv(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const v=Wv(t,e,g);if(v)return v}return ks(g)};function Ps(e){return!!(_e(e)&&e.add)}const Qp=e=>/^\-?\d*\.?\d+$/.test(e);function lu(e,t){e.indexOf(t)===-1&&e.push(t)}function uu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class cu{constructor(){this.subscriptions=[]}add(t){return lu(this.subscriptions,t),()=>uu(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const ix=e=>!isNaN(parseFloat(e));class sx{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:s,timestamp:o}=Se;this.lastUpdated!==o&&(this.timeDelta=s,this.lastUpdated=o,G.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>G.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=ix(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new cu);const r=this.events[t].add(n);return t==="change"?()=>{r(),G.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Up(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function nr(e,t){return new sx(e,t)}const Gp=e=>t=>t.test(e),ox={test:e=>e==="auto",parse:e=>e},Kp=[Pn,I,dt,Lt,hy,fy,ox],gr=e=>Kp.find(Gp(e)),ax=[...Kp,Pe,Yt],lx=e=>ax.find(Gp(e));function ux(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,nr(n))}function cx(e,t){const n=Xs(e,t);let{transitionEnd:r={},transition:i={},...s}=n?e.makeTargetAnimatable(n,!1):{};s={...s,...r};for(const o in s){const a=jy(s[o]);ux(e,o,a)}}function dx(e,t,n){var r,i;const s=Object.keys(t).filter(a=>!e.hasValue(a)),o=s.length;if(o)for(let a=0;a<o;a++){const l=s[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),c!=null&&(typeof c=="string"&&(Qp(c)||Hp(c))?c=parseFloat(c):!lx(c)&&Yt.test(u)&&(c=bp(l,u)),e.addValue(l,nr(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function fx(e,t){return t?(t[e]||t.default||t).from:void 0}function hx(e,t,n){const r={};for(const i in e){const s=fx(i,t);if(s!==void 0)r[i]=s;else{const o=n.getValue(i);o&&(r[i]=o.get())}}return r}function px({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function mx(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Yp(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(s=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const d in a){const f=e.getValue(d),g=a[d];if(!f||g===void 0||c&&px(c,d))continue;const v={delay:n,elapsed:0,...ou(s||{},d)};if(window.HandoffAppearAnimations){const y=e.getProps()[Xh];if(y){const p=window.HandoffAppearAnimations(y,d,f,G);p!==null&&(v.elapsed=p,v.isHandoff=!0)}}let x=!v.isHandoff&&!mx(f,g);if(v.type==="spring"&&(f.getVelocity()||v.velocity)&&(x=!1),f.animation&&(x=!1),x)continue;f.start(au(d,f,g,e.shouldReduceMotion&&kn.has(d)?{type:!1}:v));const P=f.animation;Ps(l)&&(l.add(d),P.then(()=>l.remove(d))),u.push(P)}return o&&Promise.all(u).then(()=>{o&&cx(e,o)}),u}function za(e,t,n={}){const r=Xs(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const s=r?()=>Promise.all(Yp(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:d}=i;return gx(e,t,u+l,c,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[s,o]:[o,s];return l().then(()=>u())}else return Promise.all([s(),o(n.delay)])}function gx(e,t,n=0,r=0,i=1,s){const o=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(yx).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(za(u,t,{...s,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function yx(e,t){return e.sortNodePosition(t)}function vx(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>za(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=za(e,t,n);else{const i=typeof t=="function"?Xs(e,t,n.custom):t;r=Promise.all(Yp(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const xx=[...Hl].reverse(),wx=Hl.length;function Sx(e){return t=>Promise.all(t.map(({animation:n,options:r})=>vx(e,n,r)))}function Cx(e){let t=Sx(e);const n=Px();let r=!0;const i=(l,u)=>{const c=Xs(e,u);if(c){const{transition:d,transitionEnd:f,...g}=c;l={...l,...g,...f}}return l};function s(l){t=l(e)}function o(l,u){const c=e.getProps(),d=e.getVariantContext(!0)||{},f=[],g=new Set;let v={},x=1/0;for(let y=0;y<wx;y++){const p=xx[y],m=n[p],w=c[p]!==void 0?c[p]:d[p],C=qr(w),E=p===u?m.isActive:null;E===!1&&(x=y);let j=w===d[p]&&w!==c[p]&&C;if(j&&r&&e.manuallyAnimateOnMount&&(j=!1),m.protectedKeys={...v},!m.isActive&&E===null||!w&&!m.prevProp||Qs(w)||typeof w=="boolean")continue;let A=kx(m.prevProp,w)||p===u&&m.isActive&&!j&&C||y>x&&C,V=!1;const b=Array.isArray(w)?w:[w];let F=b.reduce(i,{});E===!1&&(F={});const{prevResolvedValues:J={}}=m,K={...J,...F},Le=H=>{A=!0,g.has(H)&&(V=!0,g.delete(H)),m.needsAnimating[H]=!0};for(const H in K){const ue=F[H],T=J[H];if(v.hasOwnProperty(H))continue;let R=!1;ws(ue)&&ws(T)?R=!vp(ue,T):R=ue!==T,R?ue!==void 0?Le(H):g.add(H):ue!==void 0&&g.has(H)?Le(H):m.protectedKeys[H]=!0}m.prevProp=w,m.prevResolvedValues=F,m.isActive&&(v={...v,...F}),r&&e.blockInitialAnimation&&(A=!1),A&&(!j||V)&&f.push(...b.map(H=>({animation:H,options:{type:p,...l}})))}if(g.size){const y={};g.forEach(p=>{const m=e.getBaseTarget(p);m!==void 0&&(y[p]=m)}),f.push({animation:y})}let P=!!f.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(P=!1),r=!1,P?t(f):Promise.resolve()}function a(l,u,c){var d;if(n[l].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(g=>{var v;return(v=g.animationState)===null||v===void 0?void 0:v.setActive(l,u)}),n[l].isActive=u;const f=o(c,l);for(const g in n)n[g].protectedKeys={};return f}return{animateChanges:o,setActive:a,setAnimateFunction:s,getState:()=>n}}function kx(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!vp(t,e):!1}function nn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Px(){return{animate:nn(!0),whileInView:nn(),whileHover:nn(),whileTap:nn(),whileDrag:nn(),whileFocus:nn(),exit:nn()}}class Tx extends qt{constructor(t){super(t),t.animationState||(t.animationState=Cx(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Qs(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Ex=0;class jx extends qt{constructor(){super(...arguments),this.id=Ex++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Nx={animation:{Feature:Tx},exit:{Feature:jx}},bc=(e,t)=>Math.abs(e-t);function Lx(e,t){const n=bc(e.x,t.x),r=bc(e.y,t.y);return Math.sqrt(n**2+r**2)}class Xp{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Do(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,g=Lx(d.offset,{x:0,y:0})>=3;if(!f&&!g)return;const{point:v}=d,{timestamp:x}=Se;this.history.push({...v,timestamp:x});const{onStart:P,onMove:y}=this.handlers;f||(P&&P(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Vo(f,this.transformPagePoint),G.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const P=Do(d.type==="pointercancel"?this.lastMoveEventInfo:Vo(f,this.transformPagePoint),this.history);this.startEvent&&g&&g(d,P),v&&v(d,P)},!hp(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=Ys(t),a=Vo(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=Se;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Do(a,this.history)),this.removeListeners=Ht(vt(this.contextWindow,"pointermove",this.handlePointerMove),vt(this.contextWindow,"pointerup",this.handlePointerUp),vt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Tt(this.updatePoint)}}function Vo(e,t){return t?{point:t(e.point)}:e}function Hc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Do({point:e},t){return{point:e,delta:Hc(e,Zp(t)),offset:Hc(e,Ax(t)),velocity:Rx(t,.1)}}function Ax(e){return e[0]}function Zp(e){return e[e.length-1]}function Rx(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Zp(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>mn(t)));)n--;if(!r)return{x:0,y:0};const s=xt(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Ue(e){return e.max-e.min}function Ba(e,t=0,n=.01){return Math.abs(e-t)<=n}function Qc(e,t,n,r=.5){e.origin=r,e.originPoint=ee(t.min,t.max,e.origin),e.scale=Ue(n)/Ue(t),(Ba(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=ee(n.min,n.max,e.origin)-e.originPoint,(Ba(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Dr(e,t,n,r){Qc(e.x,t.x,n.x,r?r.originX:void 0),Qc(e.y,t.y,n.y,r?r.originY:void 0)}function Gc(e,t,n){e.min=n.min+t.min,e.max=e.min+Ue(t)}function Mx(e,t,n){Gc(e.x,t.x,n.x),Gc(e.y,t.y,n.y)}function Kc(e,t,n){e.min=t.min-n.min,e.max=e.min+Ue(t)}function _r(e,t,n){Kc(e.x,t.x,n.x),Kc(e.y,t.y,n.y)}function Vx(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?ee(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?ee(n,e,r.max):Math.min(e,n)),e}function Yc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Dx(e,{top:t,left:n,bottom:r,right:i}){return{x:Yc(e.x,n,i),y:Yc(e.y,t,r)}}function Xc(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function _x(e,t){return{x:Xc(e.x,t.x),y:Xc(e.y,t.y)}}function Ix(e,t){let n=.5;const r=Ue(e),i=Ue(t);return i>r?n=ti(t.min,t.max-r,e.min):r>i&&(n=ti(e.min,e.max-i,t.min)),Kt(0,1,n)}function Ox(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Ua=.35;function Fx(e=Ua){return e===!1?e=0:e===!0&&(e=Ua),{x:Zc(e,"left","right"),y:Zc(e,"top","bottom")}}function Zc(e,t,n){return{min:Jc(e,t),max:Jc(e,n)}}function Jc(e,t){return typeof e=="number"?e:e[t]||0}const qc=()=>({translate:0,scale:1,origin:0,originPoint:0}),Un=()=>({x:qc(),y:qc()}),ed=()=>({min:0,max:0}),ae=()=>({x:ed(),y:ed()});function He(e){return[e("x"),e("y")]}function Jp({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function zx({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Bx(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function _o(e){return e===void 0||e===1}function $a({scale:e,scaleX:t,scaleY:n}){return!_o(e)||!_o(t)||!_o(n)}function on(e){return $a(e)||qp(e)||e.z||e.rotate||e.rotateX||e.rotateY}function qp(e){return td(e.x)||td(e.y)}function td(e){return e&&e!=="0%"}function Ts(e,t,n){const r=e-n,i=t*r;return n+i}function nd(e,t,n,r,i){return i!==void 0&&(e=Ts(e,i,r)),Ts(e,n,r)+t}function Wa(e,t=0,n=1,r,i){e.min=nd(e.min,t,n,r,i),e.max=nd(e.max,t,n,r,i)}function em(e,{x:t,y:n}){Wa(e.x,t.translate,t.scale,t.originPoint),Wa(e.y,n.translate,n.scale,n.originPoint)}function Ux(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let a=0;a<i;a++){s=n[a],o=s.projectionDelta;const l=s.instance;l&&l.style&&l.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&$n(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,em(e,o)),r&&on(s.latestValues)&&$n(e,s.latestValues))}t.x=rd(t.x),t.y=rd(t.y)}function rd(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Mt(e,t){e.min=e.min+t,e.max=e.max+t}function id(e,t,[n,r,i]){const s=t[i]!==void 0?t[i]:.5,o=ee(e.min,e.max,s);Wa(e,t[n],t[r],o,t.scale)}const $x=["x","scaleX","originX"],Wx=["y","scaleY","originY"];function $n(e,t){id(e.x,t,$x),id(e.y,t,Wx)}function tm(e,t){return Jp(Bx(e.getBoundingClientRect(),t))}function bx(e,t,n){const r=tm(e,n),{scroll:i}=t;return i&&(Mt(r.x,i.offset.x),Mt(r.y,i.offset.y)),r}const nm=({current:e})=>e?e.ownerDocument.defaultView:null,Hx=new WeakMap;class Qx{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ae(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Ys(c,"page").point)},s=(c,d)=>{const{drag:f,dragPropagation:g,onDragStart:v}=this.getProps();if(f&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=mp(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),He(P=>{let y=this.getAxisMotionValue(P).get()||0;if(dt.test(y)){const{projection:p}=this.visualElement;if(p&&p.layout){const m=p.layout.layoutBox[P];m&&(y=Ue(m)*(parseFloat(y)/100))}}this.originPoint[P]=y}),v&&G.update(()=>v(c,d),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(c,d)=>{const{dragPropagation:f,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:P}=d;if(g&&this.currentDirection===null){this.currentDirection=Gx(P),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,P),this.updateAxis("y",d.point,P),this.visualElement.render(),x&&x(c,d)},a=(c,d)=>this.stop(c,d),l=()=>He(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Xp(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:nm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&G.update(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Mi(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=Vx(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&zn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=Dx(i.layoutBox,n):this.constraints=!1,this.elastic=Fx(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&He(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=Ox(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!zn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=bx(r,i.root,this.visualElement.getTransformPagePoint());let o=_x(i.layout.layoutBox,s);if(n){const a=n(zx(o));this.hasMutatedConstraints=!!a,a&&(o=Jp(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=He(c=>{if(!Mi(c,n,this.currentDirection))return;let d=l&&l[c]||{};o&&(d={min:0,max:0});const f=i?200:1e6,g=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...s,...d};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(au(t,r,0,n))}stopAnimation(){He(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){He(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){He(n=>{const{drag:r}=this.getProps();if(!Mi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];s.set(t[n]-ee(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!zn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};He(o=>{const a=this.getAxisMotionValue(o);if(a){const l=a.get();i[o]=Ix({min:l,max:l},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),He(o=>{if(!Mi(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(ee(l,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;Hx.set(this.visualElement,this);const t=this.visualElement.current,n=vt(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();zn(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const o=gt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(He(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Ua,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:a}}}function Mi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Gx(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Kx extends qt{constructor(t){super(t),this.removeGroupControls=oe,this.removeListeners=oe,this.controls=new Qx(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||oe}unmount(){this.removeGroupControls(),this.removeListeners()}}const sd=e=>(t,n)=>{e&&G.update(()=>e(t,n))};class Yx extends qt{constructor(){super(...arguments),this.removePointerDownListener=oe}onPointerDown(t){this.session=new Xp(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:sd(t),onStart:sd(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&G.update(()=>i(s,o))}}}mount(){this.removePointerDownListener=vt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function Xx(){const e=S.useContext(bs);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=S.useId();return S.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Gi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function od(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const yr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(I.test(e))e=parseFloat(e);else return e;const n=od(e,t.target.x),r=od(e,t.target.y);return`${n}% ${r}%`}},Zx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Yt.parse(e);if(i.length>5)return r;const s=Yt.createTransformer(e),o=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+o]/=a,i[1+o]/=l;const u=ee(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class Jx extends Ja.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;sy(qx),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Gi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||G.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rm(e){const[t,n]=Xx(),r=S.useContext(Gl);return Ja.createElement(Jx,{...e,layoutGroup:r,switchLayoutGroup:S.useContext(Jh),isPresent:t,safeToRemove:n})}const qx={borderRadius:{...yr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:yr,borderTopRightRadius:yr,borderBottomLeftRadius:yr,borderBottomRightRadius:yr,boxShadow:Zx},im=["TopLeft","TopRight","BottomLeft","BottomRight"],e1=im.length,ad=e=>typeof e=="string"?parseFloat(e):e,ld=e=>typeof e=="number"||I.test(e);function t1(e,t,n,r,i,s){i?(e.opacity=ee(0,n.opacity!==void 0?n.opacity:1,n1(r)),e.opacityExit=ee(t.opacity!==void 0?t.opacity:1,0,r1(r))):s&&(e.opacity=ee(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<e1;o++){const a=`border${im[o]}Radius`;let l=ud(t,a),u=ud(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||ld(l)===ld(u)?(e[a]=Math.max(ee(ad(l),ad(u),r),0),(dt.test(u)||dt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=ee(t.rotate||0,n.rotate||0,r))}function ud(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const n1=sm(0,.5,jp),r1=sm(.5,.95,oe);function sm(e,t,n){return r=>r<e?0:r>t?1:n(ti(e,t,r))}function cd(e,t){e.min=t.min,e.max=t.max}function be(e,t){cd(e.x,t.x),cd(e.y,t.y)}function dd(e,t,n,r,i){return e-=t,e=Ts(e,1/n,r),i!==void 0&&(e=Ts(e,1/i,r)),e}function i1(e,t=0,n=1,r=.5,i,s=e,o=e){if(dt.test(t)&&(t=parseFloat(t),t=ee(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=ee(s.min,s.max,r);e===s&&(a-=t),e.min=dd(e.min,t,n,a,i),e.max=dd(e.max,t,n,a,i)}function fd(e,t,[n,r,i],s,o){i1(e,t[n],t[r],t[i],t.scale,s,o)}const s1=["x","scaleX","originX"],o1=["y","scaleY","originY"];function hd(e,t,n,r){fd(e.x,t,s1,n?n.x:void 0,r?r.x:void 0),fd(e.y,t,o1,n?n.y:void 0,r?r.y:void 0)}function pd(e){return e.translate===0&&e.scale===1}function om(e){return pd(e.x)&&pd(e.y)}function a1(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function am(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function md(e){return Ue(e.x)/Ue(e.y)}class l1{constructor(){this.members=[]}add(t){lu(this.members,t),t.scheduleRender()}remove(t){if(uu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function gd(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y;if((i||s)&&(r=`translate3d(${i}px, ${s}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const o=e.x.scale*t.x,a=e.y.scale*t.y;return(o!==1||a!==1)&&(r+=`scale(${o}, ${a})`),r||"none"}const u1=(e,t)=>e.depth-t.depth;class c1{constructor(){this.children=[],this.isDirty=!1}add(t){lu(this.children,t),this.isDirty=!0}remove(t){uu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(u1),this.isDirty=!1,this.children.forEach(t)}}function d1(e,t){const n=performance.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&(Tt(r),e(s-t))};return G.read(r,!0),()=>Tt(r)}function f1(e){window.MotionDebug&&window.MotionDebug.record(e)}function h1(e){return e instanceof SVGElement&&e.tagName!=="svg"}function p1(e,t,n){const r=_e(e)?e:nr(e);return r.start(au("",r,t,n)),r.animation}const yd=["","X","Y","Z"],m1={visibility:"hidden"},vd=1e3;let g1=0;const an={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function lm({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},a=t==null?void 0:t()){this.id=g1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,an.totalNodes=an.resolvedTargetDeltas=an.recalculatedProjection=0,this.nodes.forEach(x1),this.nodes.forEach(P1),this.nodes.forEach(T1),this.nodes.forEach(w1),f1(an)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new c1)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new cu),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=h1(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const f=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=d1(f,250),Gi.hasAnimatedSinceResize&&(Gi.hasAnimatedSinceResize=!1,this.nodes.forEach(wd))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||A1,{onLayoutAnimationStart:P,onLayoutAnimationComplete:y}=c.getProps(),p=!this.targetLayout||!am(this.targetLayout,v)||g,m=!f&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||f&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,m);const w={...ou(x,"layout"),onPlay:P,onComplete:y};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else f||wd(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Tt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(E1),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(xd);return}this.isUpdating||this.nodes.forEach(C1),this.isUpdating=!1,this.nodes.forEach(k1),this.nodes.forEach(y1),this.nodes.forEach(v1),this.clearAllSnapshots();const a=performance.now();Se.delta=Kt(0,1e3/60,a-Se.timestamp),Se.timestamp=a,Se.isProcessing=!0,To.update.process(Se),To.preRender.process(Se),To.render.process(Se),Se.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(S1),this.sharedNodes.forEach(j1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ae(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!om(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||on(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),R1(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return ae();const a=o.measureViewportBox(),{scroll:l}=this.root;return l&&(Mt(a.x,l.offset.x),Mt(a.y,l.offset.y)),a}removeElementScroll(o){const a=ae();be(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:d}=u;if(u!==this.root&&c&&d.layoutScroll){if(c.isRoot){be(a,o);const{scroll:f}=this.root;f&&(Mt(a.x,-f.offset.x),Mt(a.y,-f.offset.y))}Mt(a.x,c.offset.x),Mt(a.y,c.offset.y)}}return a}applyTransform(o,a=!1){const l=ae();be(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&$n(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),on(c.latestValues)&&$n(l,c.latestValues)}return on(this.latestValues)&&$n(l,this.latestValues),l}removeTransform(o){const a=ae();be(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!on(u.latestValues))continue;$a(u.latestValues)&&u.updateSnapshot();const c=ae(),d=u.measurePageBox();be(c,d),hd(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return on(this.latestValues)&&hd(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Se.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=Se.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),_r(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ae(),this.targetWithTransforms=ae()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Mx(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):be(this.target,this.layout.layoutBox),em(this.target,this.targetDelta)):be(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),_r(this.relativeTargetOrigin,this.target,g.target),be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}an.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||$a(this.parent.latestValues)||qp(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Se.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;be(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,g=this.treeScale.y;Ux(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:v}=a;if(!v){this.projectionTransform&&(this.projectionDelta=Un(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Un(),this.projectionDeltaWithTransform=Un());const x=this.projectionTransform;Dr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=gd(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==f||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),an.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=Un();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=ae(),g=l?l.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,P=this.getStack(),y=!P||P.members.length<=1,p=!!(x&&!y&&this.options.crossfade===!0&&!this.path.some(L1));this.animationProgress=0;let m;this.mixTargetDelta=w=>{const C=w/1e3;Sd(d.x,o.x,C),Sd(d.y,o.y,C),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(_r(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),N1(this.relativeTarget,this.relativeTargetOrigin,f,C),m&&a1(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=ae()),be(m,this.relativeTarget)),x&&(this.animationValues=c,t1(c,u,this.latestValues,C,p,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Tt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.update(()=>{Gi.hasAnimatedSinceResize=!0,this.currentAnimation=p1(0,vd,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(vd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&um(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ae();const d=Ue(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const f=Ue(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+f}be(a,l),$n(a,c),Dr(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new l1),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<yd.length;c++){const d="rotate"+yd[c];l[d]&&(u[d]=l[d],o.setStaticValue(d,0))}o.render();for(const c in u)o.setStaticValue(c,u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return m1;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Qi(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Qi(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!on(this.latestValues)&&(x.transform=c?c({},""):"none",this.hasProjected=!1),x}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=gd(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const x in vs){if(f[x]===void 0)continue;const{correct:P,applyTo:y}=vs[x],p=u.transform==="none"?f[x]:P(f[x],d);if(y){const m=y.length;for(let w=0;w<m;w++)u[y[w]]=p}else u[x]=p}return this.options.layoutId&&(u.pointerEvents=d===this?Qi(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(xd),this.root.sharedNodes.clear()}}}function y1(e){e.updateLayout()}function v1(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?He(d=>{const f=o?n.measuredBox[d]:n.layoutBox[d],g=Ue(f);f.min=r[d].min,f.max=f.min+g}):um(s,n.layoutBox,r)&&He(d=>{const f=o?n.measuredBox[d]:n.layoutBox[d],g=Ue(r[d]);f.max=f.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+g)});const a=Un();Dr(a,r,n.layoutBox);const l=Un();o?Dr(l,e.applyTransform(i,!0),n.measuredBox):Dr(l,r,n.layoutBox);const u=!om(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:g}=d;if(f&&g){const v=ae();_r(v,n.layoutBox,f.layoutBox);const x=ae();_r(x,r,g.layoutBox),am(v,x)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function x1(e){an.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function w1(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function S1(e){e.clearSnapshot()}function xd(e){e.clearMeasurements()}function C1(e){e.isLayoutDirty=!1}function k1(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function wd(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function P1(e){e.resolveTargetDelta()}function T1(e){e.calcProjection()}function E1(e){e.resetRotation()}function j1(e){e.removeLeadSnapshot()}function Sd(e,t,n){e.translate=ee(t.translate,0,n),e.scale=ee(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Cd(e,t,n,r){e.min=ee(t.min,n.min,r),e.max=ee(t.max,n.max,r)}function N1(e,t,n,r){Cd(e.x,t.x,n.x,r),Cd(e.y,t.y,n.y,r)}function L1(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const A1={duration:.45,ease:[.4,0,.1,1]},kd=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),Pd=kd("applewebkit/")&&!kd("chrome/")?Math.round:oe;function Td(e){e.min=Pd(e.min),e.max=Pd(e.max)}function R1(e){Td(e.x),Td(e.y)}function um(e,t,n){return e==="position"||e==="preserve-aspect"&&!Ba(md(t),md(n),.2)}const M1=lm({attachResizeListener:(e,t)=>gt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Io={current:void 0},cm=lm({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Io.current){const e=new M1({});e.mount(window),e.setOptions({layoutScroll:!0}),Io.current=e}return Io.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),V1={pan:{Feature:Yx},drag:{Feature:Kx,ProjectionNode:cm,MeasureLayout:rm}},D1=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function _1(e){const t=D1.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function ba(e,t,n=1){const[r,i]=_1(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return Qp(o)?parseFloat(o):o}else return Va(i)?ba(i,t,n+1):i}function I1(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const s=i.get();if(!Va(s))return;const o=ba(s,r);o&&i.set(o)});for(const i in t){const s=t[i];if(!Va(s))continue;const o=ba(s,r);o&&(t[i]=o,n||(n={}),n[i]===void 0&&(n[i]=s))}return{target:t,transitionEnd:n}}const O1=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),dm=e=>O1.has(e),F1=e=>Object.keys(e).some(dm),Ed=e=>e===Pn||e===I,jd=(e,t)=>parseFloat(e.split(", ")[t]),Nd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return jd(i[1],t);{const s=r.match(/^matrix\((.+)\)$/);return s?jd(s[1],e):0}},z1=new Set(["x","y","z"]),B1=ai.filter(e=>!z1.has(e));function U1(e){const t=[];return B1.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const rr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Nd(4,13),y:Nd(5,14)};rr.translateX=rr.x;rr.translateY=rr.y;const $1=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,s=getComputedStyle(i),{display:o}=s,a={};o==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=rr[u](r,s)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(a[u]),e[u]=rr[u](l,s)}),e},W1=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(dm);let s=[],o=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],d=gr(c);const f=t[l];let g;if(ws(f)){const v=f.length,x=f[0]===null?1:0;c=f[x],d=gr(c);for(let P=x;P<v&&f[P]!==null;P++)g?tu(gr(f[P])===g):g=gr(f[P])}else g=gr(f);if(d!==g)if(Ed(d)&&Ed(g)){const v=u.get();typeof v=="string"&&u.set(parseFloat(v)),typeof f=="string"?t[l]=parseFloat(f):Array.isArray(f)&&g===I&&(t[l]=f.map(parseFloat))}else d!=null&&d.transform&&(g!=null&&g.transform)&&(c===0||f===0)?c===0?u.set(g.transform(c)):t[l]=d.transform(f):(o||(s=U1(e),o=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(f))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=$1(t,e,a);return s.length&&s.forEach(([c,d])=>{e.getValue(c).set(d)}),e.render(),Hs&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function b1(e,t,n,r){return F1(t)?W1(e,t,n,r):{target:t,transitionEnd:r}}const H1=(e,t,n,r)=>{const i=I1(e,t,r);return t=i.target,r=i.transitionEnd,b1(e,t,n,r)},Ha={current:null},fm={current:!1};function Q1(){if(fm.current=!0,!!Hs)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ha.current=e.matches;e.addListener(t),t()}else Ha.current=!1}function G1(e,t,n){const{willChange:r}=t;for(const i in t){const s=t[i],o=n[i];if(_e(s))e.addValue(i,s),Ps(r)&&r.add(i);else if(_e(o))e.addValue(i,nr(s,{owner:e})),Ps(r)&&r.remove(i);else if(o!==s)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(s)}else{const a=e.getStaticValue(i);e.addValue(i,nr(a!==void 0?a:s,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Ld=new WeakMap,hm=Object.keys(ei),K1=hm.length,Ad=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Y1=Ql.length;class X1{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>G.render(this.render,!1,!0);const{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=Gs(n),this.isVariantNode=Zh(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const d in c){const f=c[d];a[d]!==void 0&&_e(f)&&(f.set(a[d],!1),Ps(u)&&u.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,Ld.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),fm.current||Q1(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ha.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ld.delete(this.current),this.projection&&this.projection.unmount(),Tt(this.notifyUpdate),Tt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=kn.has(t),i=n.on("change",o=>{this.latestValues[t]=o,this.props.onUpdate&&G.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),s()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,s){let o,a;for(let l=0;l<K1;l++){const u=hm[l],{isEnabled:c,Feature:d,ProjectionNode:f,MeasureLayout:g}=ei[u];f&&(o=f),c(n)&&(!this.features[u]&&d&&(this.features[u]=new d(this)),g&&(a=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:d,layoutScroll:f,layoutRoot:g}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||d&&zn(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:s,layoutScroll:f,layoutRoot:g})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ae()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Ad.length;r++){const i=Ad[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=G1(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<Y1;r++){const i=Ql[r],s=this.props[i];(qr(s)||s===!1)&&(n[i]=s)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=nr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=eu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!_e(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new cu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class pm extends X1{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},s){let o=hx(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),o&&(o=i(o))),s){dx(this,r,o);const a=H1(this,r,o,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function Z1(e){return window.getComputedStyle(e)}class J1 extends pm{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(kn.has(n)){const r=su(n);return r&&r.default||0}else{const r=Z1(t),i=(tp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return tm(t,n)}build(t,n,r,i){Yl(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return ql(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;_e(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){ap(t,n,r,i)}}class q1 extends pm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(kn.has(n)){const r=su(n);return r&&r.default||0}return n=lp.has(n)?n:bl(n),t.getAttribute(n)}measureInstanceViewportBox(){return ae()}scrapeMotionValuesFromProps(t,n){return cp(t,n)}build(t,n,r,i){Zl(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){up(t,n,r,i)}mount(t){this.isSVGTag=Jl(t.tagName),super.mount(t)}}const ew=(e,t)=>Kl(e)?new q1(t,{enableHardwareAcceleration:!1}):new J1(t,{enableHardwareAcceleration:!0}),tw={layout:{ProjectionNode:cm,MeasureLayout:rm}},nw={...Nx,...Ky,...V1,...tw},O=ry((e,t)=>_y(e,t,nw,ew));function mm(){const e=S.useRef(!1);return Wl(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function rw(){const e=mm(),[t,n]=S.useState(0),r=S.useCallback(()=>{e.current&&n(t+1)},[t]);return[S.useCallback(()=>G.postRender(r),[r]),t]}class iw extends S.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function sw({children:e,isPresent:t}){const n=S.useId(),r=S.useRef(null),i=S.useRef({width:0,height:0,top:0,left:0});return S.useInsertionEffect(()=>{const{width:s,height:o,top:a,left:l}=i.current;if(t||!r.current||!s||!o)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${o}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),S.createElement(iw,{isPresent:t,childRef:r,sizeRef:i},S.cloneElement(e,{ref:r}))}const Oo=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:s,mode:o})=>{const a=dp(ow),l=S.useId(),u=S.useMemo(()=>({id:l,initial:t,isPresent:n,custom:i,onExitComplete:c=>{a.set(c,!0);for(const d of a.values())if(!d)return;r&&r()},register:c=>(a.set(c,!1),()=>a.delete(c))}),s?void 0:[n]);return S.useMemo(()=>{a.forEach((c,d)=>a.set(d,!1))},[n]),S.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=S.createElement(sw,{isPresent:n},e)),S.createElement(bs.Provider,{value:u},e)};function ow(){return new Map}function aw(e){return S.useEffect(()=>()=>e(),[])}const ln=e=>e.key||"";function lw(e,t){e.forEach(n=>{const r=ln(n);t.set(r,n)})}function uw(e){const t=[];return S.Children.forEach(e,n=>{S.isValidElement(n)&&t.push(n)}),t}const ir=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:s=!0,mode:o="sync"})=>{const a=S.useContext(Gl).forceRender||rw()[0],l=mm(),u=uw(e);let c=u;const d=S.useRef(new Map).current,f=S.useRef(c),g=S.useRef(new Map).current,v=S.useRef(!0);if(Wl(()=>{v.current=!1,lw(u,g),f.current=c}),aw(()=>{v.current=!0,g.clear(),d.clear()}),v.current)return S.createElement(S.Fragment,null,c.map(p=>S.createElement(Oo,{key:ln(p),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:s,mode:o},p)));c=[...c];const x=f.current.map(ln),P=u.map(ln),y=x.length;for(let p=0;p<y;p++){const m=x[p];P.indexOf(m)===-1&&!d.has(m)&&d.set(m,void 0)}return o==="wait"&&d.size&&(c=[]),d.forEach((p,m)=>{if(P.indexOf(m)!==-1)return;const w=g.get(m);if(!w)return;const C=x.indexOf(m);let E=p;if(!E){const j=()=>{d.delete(m);const k=Array.from(g.keys()).filter(A=>!P.includes(A));if(k.forEach(A=>g.delete(A)),f.current=u.filter(A=>{const V=ln(A);return V===m||k.includes(V)}),!d.size){if(l.current===!1)return;a(),r&&r()}};E=S.createElement(Oo,{key:ln(w),isPresent:!1,onExitComplete:j,custom:t,presenceAffectsLayout:s,mode:o},w),d.set(m,E)}c.splice(C,0,E)}),c=c.map(p=>{const m=p.key;return d.has(m)?p:S.createElement(Oo,{key:ln(p),isPresent:!0,presenceAffectsLayout:s,mode:o},p)}),S.createElement(S.Fragment,null,d.size?c:c.map(p=>S.cloneElement(p)))},$=({children:e,onClick:t,variant:n="primary",size:r="md",disabled:i=!1,className:s=""})=>{const o="font-bold rounded-full shadow-lg transition-all duration-200 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed min-w-[120px]",a={primary:"bg-gradient-to-r from-[#667eea] to-[#764ba2] hover:scale-105 text-white",secondary:"bg-gradient-to-r from-[#f093fb] to-[#f5576c] hover:scale-105 text-white",success:"bg-gradient-to-r from-[#4facfe] to-[#00f2fe] hover:scale-105 text-white",warning:"bg-gradient-to-r from-[#ffa726] to-[#ff7043] hover:scale-105 text-white",danger:"bg-gradient-to-r from-[#ff4757] to-[#c44569] hover:scale-105 text-white"},l={sm:"py-2 px-4 text-sm",md:"py-[18px] px-[36px] text-[20px]",lg:"py-5 px-10 text-2xl"};return h.jsx(O.button,{whileHover:{scale:i?1:1.05},whileTap:{scale:i?1:.95},className:`${o} ${a[n]} ${l[r]} ${s}`,onClick:t,disabled:i,children:e})},it=({children:e,className:t="",onClick:n,hoverable:r=!1})=>{const i="bg-white rounded-2xl shadow-xl p-6 transition-all duration-300",s=r?"hover:shadow-2xl cursor-pointer":"";return h.jsx(O.div,{whileHover:r?{scale:1.05,y:-5}:{},whileTap:r?{scale:.95}:{},className:`${i} ${s} ${t}`,onClick:n,children:e})},cw=({children:e,title:t,showBackButton:n=!1,onBack:r})=>h.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 p-4",children:h.jsxs("div",{className:"max-w-4xl mx-auto",children:[(t||n)&&h.jsxs(O.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-6",children:[n&&h.jsxs("button",{onClick:r,className:"flex items-center space-x-2 text-white hover:text-yellow-300 transition-colors",children:[h.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),h.jsx("span",{children:"返回"})]}),t&&h.jsx("h1",{className:"text-3xl font-bold text-white text-center flex-1",children:t}),n&&h.jsx("div",{className:"w-16"})]}),h.jsx(O.main,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{delay:.1},children:e})]})}),wt=({number:e,placeholder:t="?",className:n="",isAnswer:r=!1,showAnimation:i=!1})=>{const s=e!==void 0?e:t,o="number-card min-w-[80px] min-h-[80px] flex items-center justify-center text-[48px] font-bold",a=r?"bg-gradient-to-br from-success-100 to-success-200 border-success-300":"";return h.jsx(O.div,{className:`${o} ${a} ${n}`,initial:i?{scale:0,rotate:-180}:{},animate:i?{scale:1,rotate:0}:{},transition:{duration:.5,type:"spring",stiffness:200},children:s})},du=({onNumberClick:e,onClear:t,onConfirm:n,confirmDisabled:r=!1})=>{const i=[1,2,3,4,5,6,7,8,9,0];return h.jsxs("div",{className:"grid grid-cols-3 gap-[15px] max-w-[400px] mx-auto",children:[i.slice(0,9).map(s=>h.jsx(O.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:h.jsx($,{onClick:()=>e(s),className:"w-[80px] h-[80px] text-[36px] font-bold",variant:"secondary",children:s})},s)),h.jsx(O.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:h.jsx($,{onClick:t,className:"w-[80px] h-[80px] text-[16px] font-bold",variant:"warning",children:"清除"})}),h.jsx(O.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:h.jsx($,{onClick:()=>e(0),className:"w-[80px] h-[80px] text-[36px] font-bold",variant:"secondary",children:"0"})}),h.jsx(O.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:h.jsx($,{onClick:n,className:"w-[80px] h-[80px] text-[16px] font-bold",variant:"success",disabled:r,children:"确定"})})]})},fu=({current:e,total:t,className:n=""})=>{const r=t>0?e/t*100:0;return h.jsxs("div",{className:`w-full ${n}`,children:[h.jsxs("div",{className:"flex justify-between items-center mb-2",children:[h.jsxs("span",{className:"text-sm font-medium text-gray-700",children:["进度: ",e,"/",t]}),h.jsxs("span",{className:"text-sm font-medium text-gray-700",children:[Math.round(r),"%"]})]}),h.jsx("div",{className:"w-full bg-gray-200 rounded-full h-5 overflow-hidden",children:h.jsx(O.div,{className:"h-full bg-gradient-to-r from-success-400 to-success-600 rounded-full",initial:{width:0},animate:{width:`${r}%`},transition:{duration:.5,ease:"easeOut"}})})]})},Rd=({rating:e,maxRating:t=3,size:n="md",className:r=""})=>{const i={sm:"w-6 h-6",md:"w-8 h-8",lg:"w-12 h-12"};return h.jsx("div",{className:`flex space-x-1 ${r}`,children:Array.from({length:t},(s,o)=>h.jsx(O.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:o*.1,duration:.3},className:`${i[n]} flex-shrink-0`,children:h.jsx("svg",{className:`w-full h-full ${o<e?"text-yellow-400 fill-current":"text-gray-300 fill-current"}`,viewBox:"0 0 24 24",children:h.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})},o))})},dw=({show:e,type:t,message:n})=>{const r={correct:{initial:{scale:0,rotate:-180,opacity:0},animate:{scale:1,rotate:0,opacity:1},exit:{scale:0,rotate:180,opacity:0},transition:{duration:.6,type:"spring",stiffness:200}},incorrect:{initial:{x:-20,opacity:0},animate:{x:[0,-10,10,-10,10,0],opacity:1},exit:{x:20,opacity:0},transition:{duration:.6}},celebration:{initial:{scale:0,y:50,opacity:0},animate:{scale:[0,1.2,1],y:[50,-20,0],opacity:1,rotate:[0,10,-10,0]},exit:{scale:0,y:-50,opacity:0},transition:{duration:1,type:"spring",stiffness:100}}},i={correct:"text-green-500",incorrect:"text-red-500",celebration:"text-yellow-500"},s={correct:"✅",incorrect:"❌",celebration:"🎉"},o={correct:"bg-green-100 border-green-300",incorrect:"bg-red-100 border-red-300",celebration:"bg-yellow-100 border-yellow-300"};return h.jsx(ir,{children:e&&h.jsx(O.div,{className:"fixed inset-0 flex items-center justify-center pointer-events-none z-50",...r[t],children:h.jsxs("div",{className:`${o[t]} rounded-2xl p-8 border-2 shadow-xl`,children:[h.jsx("div",{className:`text-6xl ${i[t]} text-center mb-4`,children:s[t]}),n&&h.jsx("div",{className:`text-2xl font-bold ${i[t]} text-center`,children:n})]})})})},fw=({trigger:e,type:t="success",duration:n=2e3})=>{const[r,i]=S.useState([]),s={success:["⭐","✨","🌟","💫"],celebration:["🎉","🎊","🎈","🎁","🏆","👏"]},o={success:["#FFD700","#FFA500","#FF6347","#FF1493"],celebration:["#FF69B4","#00CED1","#98FB98","#DDA0DD"]};return S.useEffect(()=>{if(e){const a=[];for(let u=0;u<15;u++)a.push({id:Date.now()+u,x:Math.random()*window.innerWidth,y:Math.random()*window.innerHeight,color:o[t][Math.floor(Math.random()*o[t].length)],emoji:s[t][Math.floor(Math.random()*s[t].length)]});i(a);const l=setTimeout(()=>{i([])},n);return()=>clearTimeout(l)}},[e,t,n]),h.jsx("div",{className:"fixed inset-0 pointer-events-none z-40",children:h.jsx(ir,{children:r.map(a=>h.jsx(O.div,{className:"absolute text-2xl",initial:{x:a.x,y:a.y,scale:0,rotate:0,opacity:1},animate:{y:a.y-200,scale:[0,1,.8,0],rotate:[0,180,360],opacity:[1,1,.5,0]},exit:{opacity:0,scale:0},transition:{duration:n/1e3,ease:"easeOut"},style:{color:a.color},children:a.emoji},a.id))})})},hw=({onAuthenticated:e})=>{const[t,n]=S.useState(""),[r,i]=S.useState(!1),s="0401";S.useEffect(()=>{const l=localStorage.getItem("mathGameAuth"),u=sessionStorage.getItem("mathGameAuth"),c=/iPad|iPhone|iPod/.test(navigator.userAgent);(c&&l==="true"||!c&&u==="true")&&e()},[e]);const o=l=>{l.preventDefault(),t===s?(/iPad|iPhone|iPod/.test(navigator.userAgent)?localStorage.setItem("mathGameAuth","true"):sessionStorage.setItem("mathGameAuth","true"),e(),i(!1)):(i(!0),n(""),setTimeout(()=>{i(!1)},3e3))},a=()=>{n(""),i(!1)};return h.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 via-purple-600 to-purple-800",children:h.jsx(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md",children:h.jsxs(it,{className:"text-center",children:[h.jsxs("div",{className:"mb-8",children:[h.jsx("div",{className:"text-4xl mb-4",children:"🔐"}),h.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"访问验证"}),h.jsx("p",{className:"text-gray-600",children:"请输入访问密码"})]}),h.jsxs("form",{onSubmit:o,className:"space-y-4",children:[h.jsx("div",{children:h.jsx("input",{type:"password",value:t,onChange:l=>n(l.target.value),placeholder:"请输入密码",maxLength:10,className:"w-48 px-4 py-3 text-lg text-center border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none",autoFocus:!0})}),r&&h.jsx(O.div,{initial:{opacity:0},animate:{opacity:1},className:"text-red-500 text-sm",children:"密码错误，请重试"}),h.jsxs("div",{className:"flex justify-center space-x-4",children:[h.jsx($,{type:"button",variant:"secondary",onClick:a,children:"清除"}),h.jsx($,{type:"submit",variant:"primary",children:"确认"})]})]}),h.jsx("div",{className:"mt-6 text-xs text-gray-500",children:"输入密码后即可开始数学练习"})]})})})},Md=({onModeSelect:e})=>{const t=[{id:"practice",name:"练习模式",description:"自由练习，巩固基础",icon:"📚",color:"from-blue-400 to-blue-600"},{id:"adventure",name:"通关模式",description:"闯关挑战，循序渐进",icon:"🏆",color:"from-green-400 to-green-600"},{id:"wrong",name:"错题本",description:"专门练习错误题目",icon:"📖",color:"from-orange-400 to-red-500"}];return h.jsxs("div",{className:"text-center",children:[h.jsxs(O.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},transition:{duration:.8},children:[h.jsx("h1",{className:"text-6xl font-bold text-white mb-4",children:"数学小天才"}),h.jsx("p",{className:"text-xl text-white/90 mb-12",children:"让数学变得更有趣！"})]}),h.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:t.map((n,r)=>h.jsx(O.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{delay:r*.2,duration:.6},children:h.jsx(it,{hoverable:!0,onClick:()=>e(n.id),children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:"text-6xl mb-4",children:n.icon}),h.jsx("h3",{className:"text-2xl font-bold text-gray-800 mb-2",children:n.name}),h.jsx("p",{className:"text-gray-600 mb-6",children:n.description}),h.jsx("div",{className:`w-full h-2 bg-gradient-to-r ${n.color} rounded-full mb-4`}),h.jsx($,{variant:"primary",className:"w-full",onClick:i=>{i.stopPropagation(),e(n.id)},children:"开始游戏"})]})})},n.id))}),h.jsxs(O.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1,duration:.6},className:"mt-12",children:[h.jsx("p",{className:"text-white/80 text-sm",children:"适合小学生的数学加减法练习游戏"}),h.jsx("p",{className:"text-white/60 text-xs mt-2",children:"版本 v1.0.4"})]})]})},hu=[{id:"level-1-1",name:"10以内加法 - 第1关",description:"两个数相加，结果不超过10",difficultyId:"level-1",stage:1,operandRange:[1,9],operators:["+"],resultLimit:10,allowCarry:!1},{id:"level-1-2",name:"10以内加法 - 第2关",description:"两个数相加，结果不超过10",difficultyId:"level-1",stage:2,operandRange:[1,9],operators:["+"],resultLimit:10,allowCarry:!1},{id:"level-2-1",name:"20以内加法 - 第1关",description:"两个数相加，结果不超过20",difficultyId:"level-2",stage:1,operandRange:[1,19],operators:["+"],resultLimit:20,allowCarry:!0},{id:"level-2-2",name:"20以内加法 - 第2关",description:"两个数相加，结果不超过20",difficultyId:"level-2",stage:2,operandRange:[1,19],operators:["+"],resultLimit:20,allowCarry:!0},{id:"level-3-1",name:"10以内加减法 - 第1关",description:"10以内的加法和减法运算",difficultyId:"level-3",stage:1,operandRange:[1,9],operators:["+","-"],resultLimit:10,allowCarry:!1,allowBorrow:!1},{id:"level-3-2",name:"10以内加减法 - 第2关",description:"10以内的加法和减法运算",difficultyId:"level-3",stage:2,operandRange:[1,10],operators:["+","-"],resultLimit:10,allowCarry:!1,allowBorrow:!1},{id:"level-4-1",name:"20以内加减法 - 第1关",description:"20以内的加法和减法运算",difficultyId:"level-4",stage:1,operandRange:[1,19],operators:["+","-"],resultLimit:20,allowCarry:!0,allowBorrow:!0},{id:"level-4-2",name:"20以内加减法 - 第2关",description:"20以内的加法和减法运算",difficultyId:"level-4",stage:2,operandRange:[1,20],operators:["+","-"],resultLimit:20,allowCarry:!0,allowBorrow:!0},{id:"level-5-1",name:"100以内加法 - 第1关",description:"简单的100以内加法练习",difficultyId:"level-5",stage:1,operandRange:[1,50],operators:["+"],allowCarry:!0},{id:"level-5-2",name:"100以内加法 - 第2关",description:"进阶的100以内加法练习",difficultyId:"level-5",stage:2,operandRange:[1,100],operators:["+"],allowCarry:!0},{id:"level-6-1",name:"100以内加减法 - 第1关",description:"简单的100以内加减法练习",difficultyId:"level-6",stage:1,operandRange:[1,50],operators:["+","-"],allowCarry:!0,allowBorrow:!0},{id:"level-6-2",name:"100以内加减法 - 第2关",description:"进阶的100以内加减法练习",difficultyId:"level-6",stage:2,operandRange:[1,100],operators:["+","-"],allowCarry:!0,allowBorrow:!0}],pu=[{id:"level-1",name:"10以内加法",description:"两个数相加，结果不超过10",operandRange:[1,10],operators:["+"],resultLimit:10,allowCarry:!1},{id:"level-2",name:"20以内加法",description:"两个数相加，结果不超过20",operandRange:[1,20],operators:["+"],resultLimit:20,allowCarry:!0},{id:"level-3",name:"10以内加减法",description:"10以内的加法和减法运算",operandRange:[1,10],operators:["+","-"],allowCarry:!1,allowBorrow:!1},{id:"level-4",name:"20以内加减法",description:"20以内的加法和减法运算",operandRange:[1,20],operators:["+","-"],allowCarry:!0,allowBorrow:!0},{id:"level-5",name:"100以内加法",description:"100以内的加法运算",operandRange:[1,100],operators:["+"],allowCarry:!0},{id:"level-6",name:"100以内加减法",description:"100以内的加法和减法运算",operandRange:[1,100],operators:["+","-"],allowCarry:!0,allowBorrow:!0}];function Es(e,t){return Math.floor(Math.random()*(t-e+1))+e}function gm(e,t){const[n,r]=e;let i,s,o;do i=Es(n,r),s=Es(n,r),o=i+s;while(t&&o>t);return{id:`add_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,operand1:i,operand2:s,operator:"+",answer:o}}function ym(e,t){const[n,r]=e;let i,s,o;do i=Es(n,r),s=Es(n,i),o=i-s;while(t&&o>t||o<0);return{id:`sub_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,operand1:i,operand2:s,operator:"-",answer:o}}function pw(e){const t=pu.find(r=>r.id===e);if(!t)throw new Error(`Unknown difficulty level: ${e}`);return t.operators[Math.floor(Math.random()*t.operators.length)]==="+"?gm(t.operandRange,t.resultLimit):ym(t.operandRange,t.resultLimit)}function mw(e){const t=hu.find(r=>r.id===e);if(!t)throw new Error(`Unknown level: ${e}`);return t.operators[Math.floor(Math.random()*t.operators.length)]==="+"?gm(t.operandRange,t.resultLimit):ym(t.operandRange,t.resultLimit)}function Vd(e,t){const n=[];for(let r=0;r<t;r++)n.push(mw(e));return n}function gw(e){return hu.filter(t=>t.difficultyId===e)}function mu(e,t){return e.answer===t}function gu(e){const t=e.length,n=e.filter(s=>s.isCorrect===!0).length,r=t>0?n/t*100:0,i=Math.round(r);return{correct:n,total:t,accuracy:r,score:i}}const yw=({onDifficultySelect:e,selectedMode:t="practice"})=>{const n={practice:"练习模式",adventure:"通关模式",wrong:"错题本"},r=s=>{const o=["from-green-400 to-green-600","from-blue-400 to-blue-600","from-yellow-400 to-yellow-600","from-orange-400 to-orange-600","from-red-400 to-red-600","from-purple-400 to-purple-600","from-pink-400 to-pink-600"];return o[s%o.length]},i=s=>({"level-1":1,"level-2":2,"level-3":2,"level-4":3,"level-5":4,"level-6":5})[s]||1;return h.jsxs("div",{children:[h.jsxs(O.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-8",children:[h.jsx("h2",{className:"text-4xl font-bold text-white mb-4",children:"选择难度级别"}),h.jsxs("p",{className:"text-white/90 text-lg",children:["当前模式: ",n[t]]})]}),h.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:pu.map((s,o)=>h.jsx(O.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:o*.1,duration:.5},children:h.jsx(it,{hoverable:!0,onClick:()=>e(s.id),children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:`w-full h-3 bg-gradient-to-r ${r(o)} rounded-full mb-4`}),h.jsx("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:s.name}),h.jsx("p",{className:"text-gray-600 text-sm mb-4",children:s.description}),h.jsx("div",{className:"flex justify-center mb-4",children:Array.from({length:5},(a,l)=>h.jsx("svg",{className:`w-5 h-5 ${l<i(s.id)?"text-yellow-400 fill-current":"text-gray-300 fill-current"}`,viewBox:"0 0 24 24",children:h.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})},l))}),h.jsxs("div",{className:"text-xs text-gray-500 mb-4",children:[h.jsxs("div",{children:["数字范围: ",s.operandRange[0]," - ",s.operandRange[1]]}),h.jsxs("div",{children:["运算: ",s.operators.join(", ")]}),s.resultLimit&&h.jsxs("div",{children:["结果限制: ≤ ",s.resultLimit]})]}),h.jsx($,{variant:"primary",size:"sm",className:"w-full",onClick:a=>{a.stopPropagation(),e(s.id)},children:"选择这个难度"})]})})},s.id))}),h.jsx(O.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.6},className:"mt-8 text-center",children:h.jsx("p",{className:"text-white/80 text-sm",children:"💡 提示: 星星数量表示难度等级，建议从低难度开始练习"})})]})},vw=({difficultyId:e,onLevelSelect:t,onBack:n,getLevelProgress:r})=>{var u;const i=gw(e),s=(u=i[0])==null?void 0:u.difficultyId.replace("level-","级别"),o=c=>{const d=["from-green-400 to-green-600","from-blue-400 to-blue-600"];return d[(c-1)%d.length]},a=(c,d)=>{if(d===1)return!0;const f=c.replace("-2","-1");return r(f).completed},l=c=>Array.from({length:3},(d,f)=>h.jsx("svg",{className:`w-5 h-5 ${f<c?"text-yellow-400 fill-current":"text-gray-300 fill-current"}`,viewBox:"0 0 24 24",children:h.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})},f));return h.jsxs("div",{children:[h.jsxs(O.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-8",children:[h.jsx("h2",{className:"text-4xl font-bold text-white mb-4",children:"选择关卡"}),h.jsxs("p",{className:"text-white/90 text-lg",children:[s," - 通关模式"]})]}),h.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto",children:i.map((c,d)=>{const f=r(c.id),g=a(c.id,c.stage);return h.jsx(O.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:d*.1,duration:.5},children:h.jsx(it,{hoverable:g,onClick:()=>g&&t(c.id),className:`relative ${g?"":"opacity-50 cursor-not-allowed"}`,children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:`w-full h-3 bg-gradient-to-r ${o(c.stage)} rounded-full mb-4`}),!g&&h.jsx("div",{className:"absolute top-4 right-4",children:h.jsx("svg",{className:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{d:"M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6z"})})}),h.jsxs("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:["第",c.stage,"关"]}),h.jsx("p",{className:"text-gray-600 text-sm mb-4",children:c.description}),f.completed&&h.jsx("div",{className:"flex justify-center mb-4",children:l(f.stars)}),h.jsxs("div",{className:"text-xs text-gray-500 mb-4",children:[h.jsxs("div",{children:["数字范围: ",c.operandRange[0]," - ",c.operandRange[1]]}),h.jsxs("div",{children:["运算: ",c.operators.join(", ")]}),c.resultLimit&&h.jsxs("div",{children:["结果限制: ≤ ",c.resultLimit]})]}),h.jsx($,{variant:f.completed?"success":"primary",size:"sm",className:"w-full",disabled:!g,onClick:v=>{v.stopPropagation(),g&&t(c.id)},children:g?f.completed?"重新挑战":"开始挑战":"🔒 未解锁"})]})})},c.id)})}),h.jsxs(O.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.6},className:"mt-8 text-center",children:[h.jsx($,{variant:"secondary",onClick:n,children:"返回难度选择"}),h.jsx("p",{className:"text-white/80 text-sm mt-4",children:"💡 提示: 完成第1关后可解锁第2关"})]})]})},pe={USER_STATS:"math_game_user_stats",USER_PROGRESS:"math_game_user_progress",LEVEL_PROGRESS:"math_game_level_progress",SETTINGS:"math_game_settings",WRONG_QUESTIONS:"math_game_wrong_questions"},Fo={totalQuestions:0,correctAnswers:0,accuracy:0,studyTime:0,achievements:[],progress:{}},zo={sound:!0,theme:"default",difficulty:"level-1",userName:"小朋友"};class U{static getUserStats(){try{const t=localStorage.getItem(pe.USER_STATS);return t?{...Fo,...JSON.parse(t)}:Fo}catch(t){return console.error("Error loading user stats:",t),Fo}}static saveUserStats(t){try{localStorage.setItem(pe.USER_STATS,JSON.stringify(t))}catch(n){console.error("Error saving user stats:",n)}}static updateUserStats(t){const n=this.getUserStats(),r=t.filter(o=>o.isCorrect===!0).length,i=t.length,s={...n,totalQuestions:n.totalQuestions+i,correctAnswers:n.correctAnswers+r,accuracy:i>0?(n.correctAnswers+r)/(n.totalQuestions+i)*100:n.accuracy,studyTime:n.studyTime+t.reduce((o,a)=>o+(a.timeSpent||0),0)};this.saveUserStats(s)}static getUserProgress(t){try{const n=localStorage.getItem(pe.USER_PROGRESS);return(n?JSON.parse(n):{})[t]||{level:t,stars:0,completed:!1}}catch(n){return console.error("Error loading user progress:",n),{level:t,stars:0,completed:!1}}}static saveUserProgress(t,n){try{const r=localStorage.getItem(pe.USER_PROGRESS),i=r?JSON.parse(r):{};i[t]=n,localStorage.setItem(pe.USER_PROGRESS,JSON.stringify(i))}catch(r){console.error("Error saving user progress:",r)}}static updateProgress(t,n,r){const i=this.getUserProgress(t),s=r.length>0?r.filter(l=>l.isCorrect===!0).length/r.length*100:0;let o=0;s>=100?o=3:s>=90?o=2:s>=80&&(o=1);const a={...i,stars:Math.max(i.stars,o),completed:s>=80,bestScore:Math.max(i.bestScore||0,n)};this.saveUserProgress(t,a)}static getLevelProgress(t){try{const n=localStorage.getItem(pe.LEVEL_PROGRESS);return(n?JSON.parse(n):{})[t]||{levelId:t,stars:0,completed:!1,unlocked:t.endsWith("-1")}}catch{return{levelId:t,stars:0,completed:!1,unlocked:t.endsWith("-1")}}}static saveLevelProgress(t,n){try{const r=localStorage.getItem(pe.LEVEL_PROGRESS),i=r?JSON.parse(r):{};i[t]=n,localStorage.setItem(pe.LEVEL_PROGRESS,JSON.stringify(i))}catch(r){console.error("Failed to save level progress:",r)}}static updateLevelProgress(t,n,r){const i=this.getLevelProgress(t),s=r.length>0?r.filter(l=>l.isCorrect===!0).length/r.length*100:0;let o=0;s>=100?o=3:s>=90?o=2:s>=80&&(o=1);const a={...i,stars:Math.max(i.stars,o),completed:s>=80,bestScore:Math.max(i.bestScore||0,n),unlocked:!0};if(this.saveLevelProgress(t,a),t.endsWith("-1")&&a.completed){const l=t.replace("-1","-2"),u=this.getLevelProgress(l);u.unlocked||this.saveLevelProgress(l,{...u,unlocked:!0})}}static getSettings(){try{const t=localStorage.getItem(pe.SETTINGS);return t?{...zo,...JSON.parse(t)}:zo}catch(t){return console.error("Error loading settings:",t),zo}}static saveSettings(t){try{localStorage.setItem(pe.SETTINGS,JSON.stringify(t))}catch(n){console.error("Error saving settings:",n)}}static getWrongQuestions(){try{const t=localStorage.getItem(pe.WRONG_QUESTIONS);return t?JSON.parse(t):[]}catch(t){return console.error("Error loading wrong questions:",t),[]}}static addWrongQuestions(t,n){try{const r=this.getWrongQuestions(),i=t.filter(a=>a.isCorrect===!1).map(a=>({...a,source:n||a.source||"practice"})),o=[...r,...i].filter((a,l,u)=>u.findIndex(c=>c.operand1===a.operand1&&c.operand2===a.operand2&&c.operator===a.operator)===l).slice(-50);localStorage.setItem(pe.WRONG_QUESTIONS,JSON.stringify(o))}catch(r){console.error("Error saving wrong questions:",r)}}static clearWrongQuestions(){try{localStorage.removeItem(pe.WRONG_QUESTIONS)}catch(t){console.error("Error clearing wrong questions:",t)}}static exportData(){try{const t={stats:this.getUserStats(),settings:this.getSettings(),wrongQuestions:this.getWrongQuestions(),progress:localStorage.getItem(pe.USER_PROGRESS),exportDate:new Date().toISOString()};return JSON.stringify(t,null,2)}catch(t){return console.error("Error exporting data:",t),""}}static importData(t){try{const n=JSON.parse(t);return n.stats&&this.saveUserStats(n.stats),n.settings&&this.saveSettings(n.settings),n.progress&&localStorage.setItem(pe.USER_PROGRESS,n.progress),n.wrongQuestions&&localStorage.setItem(pe.WRONG_QUESTIONS,JSON.stringify(n.wrongQuestions)),!0}catch(n){return console.error("Error importing data:",n),!1}}static clearAllData(){try{Object.values(pe).forEach(t=>{localStorage.removeItem(t)})}catch(t){console.error("Error clearing all data:",t)}}}const yu=()=>{const[e,t]=S.useState(()=>U.getUserStats()),[n,r]=S.useState(()=>U.getSettings());return{userStats:e,settings:n,updateStats:g=>{U.updateUserStats(g),U.addWrongQuestions(g),t(U.getUserStats())},updateProgress:(g,v,x)=>{U.updateProgress(g,v,x)},updateSettings:g=>{U.saveSettings(g),r(g)},getProgress:g=>U.getUserProgress(g),getWrongQuestions:()=>U.getWrongQuestions(),clearWrongQuestions:()=>{U.clearWrongQuestions()},exportData:()=>U.exportData(),importData:g=>{const v=U.importData(g);return v&&(t(U.getUserStats()),r(U.getSettings())),v},clearAllData:()=>{U.clearAllData(),t(U.getUserStats()),r(U.getSettings())}}};class xw{constructor(){jt(this,"sounds",new Map);jt(this,"correctSounds",[]);jt(this,"incorrectSounds",[]);jt(this,"enabled",!0);jt(this,"audioContext",null);jt(this,"isUnlocked",!1);jt(this,"unlockAudio",null);this.initAudioContext(),this.setupUserInteractionUnlock(),this.loadCustomSounds(),this.createSounds()}initAudioContext(){try{this.audioContext=new(window.AudioContext||window.webkitAudioContext)}catch(t){console.warn("Audio not supported:",t)}}setupUserInteractionUnlock(){const t=async()=>{if(this.audioContext)try{this.audioContext.state==="suspended"&&await this.audioContext.resume();const n=this.audioContext.createOscillator(),r=this.audioContext.createGain();n.connect(r),r.connect(this.audioContext.destination),r.gain.setValueAtTime(0,this.audioContext.currentTime),n.start(this.audioContext.currentTime),n.stop(this.audioContext.currentTime+.01),this.isUnlocked=!0,console.log("Audio unlocked successfully"),this.showAudioUnlockedNotification(),this.removeUnlockListeners()}catch(n){console.warn("Failed to unlock audio:",n)}};this.unlockAudio=t,["touchstart","touchend","mousedown","keydown","click","tap"].forEach(n=>{document.addEventListener(n,t,{passive:!0})}),document.addEventListener("visibilitychange",t,{passive:!0}),window.addEventListener("focus",t,{passive:!0})}removeUnlockListeners(){this.unlockAudio&&(["touchstart","touchend","mousedown","keydown","click","tap"].forEach(t=>{document.removeEventListener(t,this.unlockAudio)}),document.removeEventListener("visibilitychange",this.unlockAudio),window.removeEventListener("focus",this.unlockAudio))}showAudioUnlockedNotification(){if(/iPhone|iPad|iPod|Safari/.test(navigator.userAgent)){const t=document.createElement("div");t.style.cssText=`
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 9999;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      `,t.textContent="🔊 音效已启用",document.body.appendChild(t),setTimeout(()=>{document.body.contains(t)&&document.body.removeChild(t)},3e3)}}async loadCustomSounds(){await this.loadSoundGroup("correct",this.correctSounds),await this.loadSoundGroup("incorrect",this.incorrectSounds)}async loadSoundGroup(t,n){let r=1;for(;;)try{const i=new Audio(`/sounds/${t}${r}.mp3`);await new Promise((s,o)=>{const a=setTimeout(()=>o(new Error("timeout")),3e3);i.addEventListener("loadeddata",()=>{clearTimeout(a),s(!0)},{once:!0}),i.addEventListener("error",()=>{clearTimeout(a),o(new Error("load failed"))},{once:!0}),i.preload="auto",i.volume=.7,i.load()}),n.push(i),console.log(`Loaded ${t} sound ${r}`),r++}catch{r===1?console.warn(`No ${t} sound files found`):console.log(`Loaded ${r-1} ${t} sound files`);break}}getRandomSound(t){if(t.length===0)return null;const n=Math.floor(Math.random()*t.length);return t[n]}createSounds(){this.createCorrectSound(),this.createIncorrectSound(),this.createClickSound(),this.createCompleteSound()}createCorrectSound(){const t=(r,i,s=0)=>new Promise(o=>{setTimeout(()=>{if(!this.audioContext){o();return}const a=this.audioContext.createOscillator(),l=this.audioContext.createGain();a.connect(l),l.connect(this.audioContext.destination),a.frequency.setValueAtTime(r,this.audioContext.currentTime),a.type="sine",l.gain.setValueAtTime(0,this.audioContext.currentTime),l.gain.linearRampToValueAtTime(.5,this.audioContext.currentTime+.01),l.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+i),a.start(this.audioContext.currentTime),a.stop(this.audioContext.currentTime+i),setTimeout(o,i*1e3)},s)}),n=async()=>{if(this.audioContext){if(this.audioContext.state==="suspended")try{await this.audioContext.resume()}catch(r){console.warn("Failed to resume audio context:",r);return}await Promise.all([t(523.25,.5),t(659.25,.5),t(783.99,.5)])}};this.sounds.set("correct",{play:n})}createIncorrectSound(){const t=async()=>{if(!this.audioContext)return;if(this.audioContext.state==="suspended")try{await this.audioContext.resume()}catch(i){console.warn("Failed to resume audio context:",i);return}const n=this.audioContext.createOscillator(),r=this.audioContext.createGain();n.connect(r),r.connect(this.audioContext.destination),n.frequency.setValueAtTime(200,this.audioContext.currentTime),n.frequency.linearRampToValueAtTime(150,this.audioContext.currentTime+.5),n.type="sine",r.gain.setValueAtTime(0,this.audioContext.currentTime),r.gain.linearRampToValueAtTime(.4,this.audioContext.currentTime+.01),r.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+.5),n.start(this.audioContext.currentTime),n.stop(this.audioContext.currentTime+.5)};this.sounds.set("incorrect",{play:t})}createClickSound(){const t=async()=>{if(!this.audioContext)return;if(this.audioContext.state==="suspended")try{await this.audioContext.resume()}catch(i){console.warn("Failed to resume audio context:",i);return}const n=this.audioContext.createOscillator(),r=this.audioContext.createGain();n.connect(r),r.connect(this.audioContext.destination),n.frequency.setValueAtTime(800,this.audioContext.currentTime),n.type="square",r.gain.setValueAtTime(0,this.audioContext.currentTime),r.gain.linearRampToValueAtTime(.3,this.audioContext.currentTime+.01),r.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+.1),n.start(this.audioContext.currentTime),n.stop(this.audioContext.currentTime+.1)};this.sounds.set("click",{play:t})}createCompleteSound(){const t=(r,i,s=0)=>new Promise(o=>{setTimeout(()=>{if(!this.audioContext){o();return}const a=this.audioContext.createOscillator(),l=this.audioContext.createGain();a.connect(l),l.connect(this.audioContext.destination),a.frequency.setValueAtTime(r,this.audioContext.currentTime),a.type="sine",l.gain.setValueAtTime(0,this.audioContext.currentTime),l.gain.linearRampToValueAtTime(.4,this.audioContext.currentTime+.01),l.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+i),a.start(this.audioContext.currentTime),a.stop(this.audioContext.currentTime+i),setTimeout(o,i*1e3)},s)}),n=async()=>{if(!this.audioContext)return;if(this.audioContext.state==="suspended")try{await this.audioContext.resume()}catch(i){console.warn("Failed to resume audio context:",i);return}const r=[523.25,587.33,659.25,698.46,783.99,880,987.77,1046.5];for(let i=0;i<r.length;i++)t(r[i],.2,i*100)};this.sounds.set("complete",{play:n})}async play(t){if(!this.enabled)return;if(await this.ensureAudioContextActive(),t==="correct"){const r=this.getRandomSound(this.correctSounds);if(r)try{r.readyState<2&&(r.load(),await new Promise(i=>{r.addEventListener("canplaythrough",i,{once:!0})})),r.currentTime=0,await r.play();return}catch(i){console.warn("Custom correct sound failed, falling back to synthesized:",i)}}else if(t==="incorrect"){const r=this.getRandomSound(this.incorrectSounds);if(r)try{r.readyState<2&&(r.load(),await new Promise(i=>{r.addEventListener("canplaythrough",i,{once:!0})})),r.currentTime=0,await r.play();return}catch(i){console.warn("Custom incorrect sound failed, falling back to synthesized:",i)}}const n=this.sounds.get(t);if(n&&n.play)try{await n.play()}catch(r){console.warn("Sound play failed:",r)}}async ensureAudioContextActive(){if(this.audioContext&&this.audioContext.state==="suspended")try{await this.audioContext.resume(),console.log("AudioContext resumed")}catch(t){console.warn("Failed to resume AudioContext:",t)}}setEnabled(t){this.enabled=t}isEnabled(){return this.enabled}}const Vi=new xw,vu=()=>{const e=S.useCallback(async r=>{try{await Vi.play(r)}catch(i){console.warn("Failed to play sound:",i)}},[]),t=S.useCallback(()=>{const r=Vi.isEnabled();return Vi.setEnabled(!r),!r},[]),n=S.useCallback(()=>Vi.isEnabled(),[]);return{playSound:e,toggleSound:t,isSoundEnabled:n}},ww=({difficulty:e,onBack:t})=>{const[n,r]=S.useState(null),[i,s]=S.useState(""),[o,a]=S.useState([]),[l,u]=S.useState(!1),[c,d]=S.useState(""),[f,g]=S.useState(0),[v,x]=S.useState(null),[P,y]=S.useState(Date.now()),[p,m]=S.useState(!1),[w,C]=S.useState(!1),[E,j]=S.useState(null),{updateStats:k,updateProgress:A}=yu(),{playSound:V}=vu(),b=pu.find(L=>L.id===e),F=10;S.useEffect(()=>{J(),y(Date.now())},[e]);const J=()=>{const L=pw(e);r(L),s(""),u(!1),x(null),m(!1)},K=L=>{i.length<3&&(V("click"),s(D=>D+L.toString()))},Le=()=>{s("")},H=()=>{if(!n||!i)return;const L=parseInt(i),D=mu(n,L),re={...n,userAnswer:L,isCorrect:D,timeSpent:0};a(ve=>[...ve,re]),x(D),D?(g(ve=>ve+1),d(T()),V("correct")):(g(0),d(`正确答案是 ${n.answer}`),V("incorrect"),U.addWrongQuestions([re],"practice")),u(!0),m(!0)},ue=()=>{o.length>=F?R():J()},T=()=>f>=5?"🎉 连续答对！你真棒！":f>=3?"👍 很好！继续保持！":"✅ 答对了！",R=()=>{const L={...n,userAnswer:parseInt(i),isCorrect:v,timeSpent:Math.round((Date.now()-P)/1e3)},D=[...o,L],re=gu(D);k(D),A(e,re.score,D),U.addWrongQuestions(D,"practice"),V("complete"),console.log("Practice completed:",re),j(re),C(!0),u(!1),m(!1)},_=()=>{if(o.length>=F-1){alert("已经是最后一题了，不能再跳过！");return}if(n){const L={...n,userAnswer:void 0,isCorrect:void 0,timeSpent:0};a(D=>[...D,L]),g(0),ue()}};return w&&E?h.jsx("div",{className:"max-w-2xl mx-auto",children:h.jsx(O.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.5},className:"text-center",children:h.jsxs(it,{className:"p-8",children:[h.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-6",children:"🎉 练习完成！"}),h.jsxs(O.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.3,duration:.6},className:"mb-8",children:[h.jsx("div",{className:"text-8xl font-bold text-red-500 mb-2",children:E.score}),h.jsx("div",{className:"text-2xl text-gray-600",children:"分"})]}),h.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-8 text-center",children:[h.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[h.jsxs("div",{className:"text-xl font-bold text-blue-600",children:[E.correct,"/",E.total]}),h.jsx("div",{className:"text-blue-700 text-sm",children:"答对题数"})]}),h.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[h.jsxs("div",{className:"text-xl font-bold text-green-600",children:[Math.round(E.accuracy),"%"]}),h.jsx("div",{className:"text-green-700 text-sm",children:"正确率"})]})]}),h.jsxs("div",{className:"space-y-4",children:[h.jsx($,{variant:"primary",onClick:t,children:"返回选择难度"}),h.jsx($,{variant:"success",onClick:()=>{C(!1),j(null),a([]),J()},children:"再来一次"})]})]})})}):n?(o.length/F*100,h.jsxs("div",{className:"max-w-2xl mx-auto",children:[h.jsxs(O.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-6",children:[h.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"练习模式"}),h.jsx("p",{className:"text-white/80",children:b==null?void 0:b.name}),h.jsxs("div",{className:"mt-4",children:[h.jsx(fu,{current:o.length,total:F,className:"mb-2"}),f>0&&h.jsxs("div",{className:"text-yellow-300 font-bold",children:["🔥 连续答对 ",f," 题！"]})]})]}),h.jsx(it,{className:"mb-6",children:h.jsxs("div",{className:"text-center",children:[h.jsxs(O.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"flex items-center justify-center space-x-4 mb-8",children:[h.jsx(wt,{number:n.operand1}),h.jsx("div",{className:"text-4xl font-bold text-gray-700",children:n.operator}),h.jsx(wt,{number:n.operand2}),h.jsx("div",{className:"text-4xl font-bold text-gray-700",children:"="}),h.jsx(wt,{number:i||void 0,placeholder:"?",isAnswer:!0,showAnimation:!!i})]},n.id),h.jsx(ir,{children:l&&h.jsx(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`p-8 rounded-2xl mb-6 ${v?"bg-green-100 text-green-800 border-2 border-green-300":"bg-red-100 text-red-800 border-2 border-red-300"}`,children:h.jsxs("div",{className:"flex items-center justify-center space-x-4",children:[h.jsx("div",{className:`text-8xl ${v?"text-green-600":"text-red-600"}`,children:v?"✓":"✗"}),h.jsx("div",{className:"text-2xl font-bold text-center",children:c})]})})}),!l&&h.jsxs(O.div,{initial:{opacity:0},animate:{opacity:1},children:[h.jsx(du,{onNumberClick:K,onClear:Le,onConfirm:H,confirmDisabled:!i}),h.jsx("div",{className:"mt-4 flex justify-center space-x-4",children:h.jsx($,{variant:"warning",size:"sm",onClick:_,children:"跳过这题"})})]}),p&&h.jsx(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mt-4",children:h.jsx($,{variant:"success",onClick:ue,children:o.length>=F?"完成练习":"下一题"})})]})}),h.jsx("div",{className:"text-center",children:h.jsx($,{variant:"secondary",onClick:t,children:"返回选择难度"})})]})):h.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:h.jsx("div",{className:"text-white text-xl",children:"正在加载题目..."})})},Sw=({levelId:e,onBack:t})=>{const[n,r]=S.useState(0),[i,s]=S.useState(""),[o,a]=S.useState([]),[l,u]=S.useState([]),[c,d]=S.useState(!1),[f,g]=S.useState(null),[v,x]=S.useState(!1),[P,y]=S.useState(null),[p,m]=S.useState(!1),[w,C]=S.useState(!1),[E,j]=S.useState(!1),{updateStats:k}=yu(),{playSound:A}=vu(),V=hu.find(L=>L.id===e),b=10;S.useEffect(()=>{const L=Vd(e,b);a(L),r(0),u([]),x(!1),y(null)},[e]);const F=o[n],J=L=>{i.length<3&&(A("click"),s(D=>D+L.toString()))},K=()=>{s("")},Le=()=>{if(!F||!i)return;const L=parseInt(i),D=mu(F,L),re={...F,userAnswer:L,isCorrect:D,timeSpent:0};u(ve=>[...ve,re]),g(D),d(!0),m(!0),C(!0),setTimeout(()=>C(!1),1500),D&&(j(!0),setTimeout(()=>j(!1),2e3)),D?A("correct"):(A("incorrect"),U.addWrongQuestions([re],"adventure"))},H=()=>{n+1>=o.length?ue([...l]):(r(L=>L+1),s(""),d(!1),g(null),m(!1))},ue=L=>{const D=gu(L);y(D),x(!0),C(!0),j(!0),setTimeout(()=>{C(!1),j(!1)},3e3),A("complete"),k(L),U.updateLevelProgress(e,D.score,L),U.addWrongQuestions(L,"adventure")},T=()=>{t()},R=()=>{const L=Vd(e,b);a(L),r(0),u([]),s(""),d(!1),g(null),x(!1),y(null),m(!1)},_=()=>P?P.accuracy>=100?3:P.accuracy>=90?2:P.accuracy>=80?1:0:0;if(!F&&!v)return h.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:h.jsx("div",{className:"text-white text-xl",children:"正在加载关卡..."})});if(v&&P){const L=_();return h.jsx("div",{className:"max-w-2xl mx-auto",children:h.jsx(it,{className:"text-center",children:h.jsxs(O.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.5},children:[h.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"🎉 关卡完成！"}),h.jsxs("div",{className:"mb-6",children:[h.jsx(Rd,{rating:L,size:"lg",className:"justify-center mb-4"}),h.jsxs("div",{className:"text-xl text-gray-700 mb-2",children:["正确率: ",Math.round(P.accuracy),"%"]}),h.jsxs("div",{className:"text-lg text-gray-600",children:["答对 ",P.correct," / ",P.total," 题"]})]}),h.jsxs("div",{className:"space-y-4",children:[L>=2&&h.jsx($,{variant:"success",size:"lg",onClick:T,children:"下一关卡"}),h.jsx($,{variant:"primary",onClick:R,children:"重新挑战"}),h.jsx($,{variant:"secondary",onClick:t,children:"返回选择"})]}),L<2&&h.jsx("div",{className:"mt-4 p-4 bg-yellow-100 border border-yellow-300 rounded-lg",children:h.jsx("p",{className:"text-yellow-800",children:"💡 需要90%以上正确率才能解锁下一关卡！"})})]})})})}return(n+(c?1:0))/o.length*100,h.jsxs("div",{className:"max-w-2xl mx-auto",children:[h.jsxs(O.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-6",children:[h.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"🏆 通关模式"}),h.jsx("p",{className:"text-white/80",children:V==null?void 0:V.name}),h.jsxs("div",{className:"mt-4",children:[h.jsx(fu,{current:n+(c?1:0),total:o.length,className:"mb-2"}),h.jsxs("div",{className:"text-white/90",children:["第 ",n+1," / ",o.length," 题"]}),U.getLevelProgress(e).stars>0&&h.jsxs("div",{className:"mt-2",children:[h.jsx(Rd,{rating:U.getLevelProgress(e).stars,className:"justify-center"}),h.jsx("span",{className:"text-white/80 text-sm ml-2",children:"历史最佳"})]})]})]}),h.jsx(it,{className:"mb-6",children:h.jsxs("div",{className:"text-center",children:[h.jsxs(O.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"flex items-center justify-center space-x-4 mb-8",children:[h.jsx(wt,{number:F==null?void 0:F.operand1}),h.jsx("div",{className:"text-4xl font-bold text-gray-700",children:F==null?void 0:F.operator}),h.jsx(wt,{number:F==null?void 0:F.operand2}),h.jsx("div",{className:"text-4xl font-bold text-gray-700",children:"="}),h.jsx(wt,{number:i||void 0,placeholder:"?",isAnswer:!0,showAnimation:!!i})]},F==null?void 0:F.id),h.jsx(ir,{children:c&&h.jsx(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`p-8 rounded-2xl mb-6 ${f?"bg-green-100 text-green-800 border-2 border-green-300":"bg-red-100 text-red-800 border-2 border-red-300"}`,children:h.jsx("div",{className:"text-3xl font-bold text-center",children:f?"✅ 答对了！":`❌ 正确答案是 ${F==null?void 0:F.answer}`})})}),!c&&h.jsx(O.div,{initial:{opacity:0},animate:{opacity:1},children:h.jsx(du,{onNumberClick:J,onClear:K,onConfirm:Le,confirmDisabled:!i})}),p&&h.jsx(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mt-4",children:h.jsx($,{variant:"success",onClick:H,children:n+1>=o.length?"完成关卡":"下一题"})})]})}),h.jsx("div",{className:"text-center",children:h.jsx($,{variant:"secondary",onClick:t,children:"退出关卡"})}),h.jsx(dw,{show:w,type:f===!0?"correct":f===!1?"incorrect":"celebration",message:v?"关卡完成！":void 0}),h.jsx(fw,{trigger:E,type:v?"celebration":"success",duration:v?3e3:2e3})]})},Cw=({onBack:e})=>{const[t,n]=S.useState([]),[r,i]=S.useState(0),[s,o]=S.useState(""),[a,l]=S.useState([]),[u,c]=S.useState(!1),[d,f]=S.useState(""),[g,v]=S.useState(null),[x,P]=S.useState(!1),[y,p]=S.useState(!1),[m,w]=S.useState(null),[C,E]=S.useState(!1),{updateStats:j}=yu(),{playSound:k}=vu();S.useEffect(()=>{const T=U.getWrongQuestions();if(T.length===0)n([]);else{const R=T.map(_=>({..._,userAnswer:void 0,isCorrect:void 0,timeSpent:0}));n(R)}},[]);const A=t[r],V=T=>{s.length<3&&(k("click"),o(R=>R+T.toString()))},b=()=>{o("")},F=()=>{if(!A||!s)return;const T=parseInt(s),R=mu(A,T),_={...A,userAnswer:T,isCorrect:R,timeSpent:0};l(L=>[...L,_]),v(R),R?(f(J()),k("correct")):(f(`正确答案是 ${A.answer}`),k("incorrect")),c(!0),P(!0)},J=()=>{const T=a.filter(R=>R.isCorrect).length;return T>=5?"🎉 连续答对！你真棒！":T>=3?"👍 很好！继续保持！":"✅ 答对了！"},K=()=>{r+1>=t.length?Le():(i(T=>T+1),o(""),c(!1),P(!1),v(null))},Le=()=>{const T=[...a];A&&s&&T.push({...A,userAnswer:parseInt(s),isCorrect:g,timeSpent:0});const R=gu(T);j(T),T.filter(D=>!D.isCorrect);const L=U.getWrongQuestions().filter(D=>!T.some(re=>re.operand1===D.operand1&&re.operand2===D.operand2&&re.operator===D.operator&&re.isCorrect));U.clearWrongQuestions(),L.length>0&&localStorage.setItem("math_game_wrong_questions",JSON.stringify(L)),k("complete"),w(R),p(!0),c(!1),P(!1)},H=()=>{if(A){const T={...A,userAnswer:void 0,isCorrect:!1,timeSpent:0};l(R=>[...R,T]),K()}},ue=()=>{U.clearWrongQuestions(),n([]),E(!1),k("complete")};return y&&m?h.jsx("div",{className:"max-w-2xl mx-auto",children:h.jsx(O.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.5},className:"text-center",children:h.jsxs(it,{className:"p-8",children:[h.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-6",children:"📖 错题练习完成！"}),h.jsxs(O.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.3,duration:.6},className:"mb-8",children:[h.jsx("div",{className:"text-8xl font-bold text-red-500 mb-2",children:m.score}),h.jsx("div",{className:"text-2xl text-gray-600",children:"分"})]}),h.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-8 text-center",children:[h.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[h.jsxs("div",{className:"text-xl font-bold text-blue-600",children:[m.correct,"/",m.total]}),h.jsx("div",{className:"text-blue-700 text-sm",children:"答对题数"})]}),h.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[h.jsxs("div",{className:"text-xl font-bold text-green-600",children:[Math.round(m.accuracy),"%"]}),h.jsx("div",{className:"text-green-700 text-sm",children:"正确率"})]})]}),h.jsxs("div",{className:"space-y-4",children:[h.jsx($,{variant:"primary",onClick:e,children:"返回主菜单"}),U.getWrongQuestions().length>0&&h.jsx($,{variant:"success",onClick:()=>{p(!1),w(null),l([]),i(0);const T=U.getWrongQuestions().map(R=>({...R,userAnswer:void 0,isCorrect:void 0,timeSpent:0}));n(T)},children:"继续练习错题"})]})]})})}):t.length===0?h.jsx("div",{className:"max-w-2xl mx-auto",children:h.jsx(it,{className:"text-center p-8",children:h.jsxs(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},children:[h.jsx("div",{className:"text-6xl mb-6",children:"🎉"}),h.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"太棒了！"}),h.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"你目前没有错题，继续保持！"}),h.jsx($,{variant:"primary",onClick:e,children:"返回主菜单"})]})})}):A?((r+1)/t.length*100,h.jsxs("div",{className:"max-w-2xl mx-auto",children:[h.jsxs(O.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-6",children:[h.jsxs("div",{className:"flex items-center justify-between mb-2",children:[h.jsx("h2",{className:"text-3xl font-bold text-white",children:"📖 错题本练习"}),h.jsx($,{variant:"warning",size:"sm",onClick:()=>E(!0),children:"清空错题本"})]}),h.jsx("p",{className:"text-white/80",children:"专门练习做错的题目"}),h.jsxs("div",{className:"mt-4",children:[h.jsx(fu,{current:r+1,total:t.length,className:"mb-2"}),h.jsxs("div",{className:"text-white/90",children:["第 ",r+1," / ",t.length," 题"]})]})]}),h.jsx(it,{className:"mb-6",children:h.jsxs("div",{className:"text-center",children:[h.jsxs(O.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"flex items-center justify-center space-x-4 mb-8",children:[h.jsx(wt,{number:A.operand1}),h.jsx("div",{className:"text-4xl font-bold text-gray-700",children:A.operator}),h.jsx(wt,{number:A.operand2}),h.jsx("div",{className:"text-4xl font-bold text-gray-700",children:"="}),h.jsx(wt,{number:s||void 0,placeholder:"?",isAnswer:!0,showAnimation:!!s})]},A.id),h.jsxs("div",{className:"text-sm text-gray-500 mb-6",children:["来源：",A.source==="practice"?"练习模式":A.source==="adventure"?"闯关模式":A.source==="test"?"测试模式":"未知"]}),h.jsx(ir,{children:u&&h.jsx(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`p-8 rounded-2xl mb-6 ${g?"bg-green-100 text-green-800 border-2 border-green-300":"bg-red-100 text-red-800 border-2 border-red-300"}`,children:h.jsxs("div",{className:"flex items-center justify-center space-x-4",children:[h.jsx("div",{className:`text-8xl ${g?"text-green-600":"text-red-600"}`,children:g?"✓":"✗"}),h.jsx("div",{className:"text-2xl font-bold text-center",children:d})]})})}),!u&&h.jsxs(O.div,{initial:{opacity:0},animate:{opacity:1},children:[h.jsx(du,{onNumberClick:V,onClear:b,onConfirm:F,confirmDisabled:!s}),h.jsx("div",{className:"mt-4 flex justify-center space-x-4",children:h.jsx($,{variant:"warning",size:"sm",onClick:H,children:"跳过这题"})})]}),x&&h.jsx(O.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mt-4",children:h.jsx($,{variant:"success",onClick:K,children:r+1>=t.length?"完成练习":"下一题"})})]})}),h.jsx("div",{className:"text-center",children:h.jsx($,{variant:"secondary",onClick:e,children:"返回主菜单"})}),h.jsx(ir,{children:C&&h.jsx(O.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",onClick:()=>E(!1),children:h.jsx(O.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"bg-white rounded-2xl p-8 max-w-md mx-4",onClick:T=>T.stopPropagation(),children:h.jsxs("div",{className:"text-center",children:[h.jsx("div",{className:"text-6xl mb-4",children:"⚠️"}),h.jsx("h3",{className:"text-2xl font-bold text-gray-800 mb-4",children:"确认清空错题本？"}),h.jsx("p",{className:"text-gray-600 mb-8",children:"这将删除所有错题记录，此操作不可撤销。"}),h.jsxs("div",{className:"flex space-x-4",children:[h.jsx($,{variant:"secondary",onClick:()=>E(!1),className:"flex-1",children:"取消"}),h.jsx($,{variant:"danger",onClick:ue,className:"flex-1",children:"确认清空"})]})]})})})})]})):h.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:h.jsx("div",{className:"text-white text-xl",children:"正在加载错题..."})})};function kw(){const[e,t]=S.useState(!1),[n,r]=S.useState({currentPage:"home"}),i=()=>{t(!0)},s=c=>{r(c==="wrong"?{...n,selectedMode:c,currentPage:"wrong"}:{...n,selectedMode:c,currentPage:"difficulty"})},o=c=>{n.selectedMode==="adventure"?r({...n,selectedDifficulty:c,currentPage:"levels"}):r({...n,selectedDifficulty:c,currentPage:n.selectedMode||"practice"})},a=c=>{r({...n,selectedLevel:c,currentPage:"adventure"})},l=()=>{switch(n.currentPage){case"difficulty":r({...n,currentPage:"home"});break;case"practice":case"adventure":case"test":r({...n,currentPage:"difficulty"});break;case"wrong":r({...n,currentPage:"home"});break;default:r({...n,currentPage:"home"})}},u=()=>{switch(n.currentPage){case"home":return h.jsx(Md,{onModeSelect:s});case"difficulty":return h.jsx(yw,{onDifficultySelect:o,selectedMode:n.selectedMode});case"levels":return h.jsx(vw,{difficultyId:n.selectedDifficulty,onLevelSelect:a,onBack:l,getLevelProgress:U.getLevelProgress});case"practice":return h.jsx(ww,{difficulty:n.selectedDifficulty,onBack:l});case"adventure":return h.jsx(Sw,{levelId:n.selectedLevel,onBack:l});case"wrong":return h.jsx(Cw,{onBack:l});default:return h.jsx(Md,{onModeSelect:s})}};return e?h.jsx(cw,{showBackButton:n.currentPage!=="home",onBack:l,children:u()}):h.jsx(hw,{onAuthenticated:i})}const Pw=async()=>{if("serviceWorker"in navigator)try{const e=await navigator.serviceWorker.register("/sw.js");console.log("Service Worker registered successfully:",e),e.update(),e.addEventListener("updatefound",()=>{const t=e.installing;t&&t.addEventListener("statechange",()=>{t.state==="installed"&&navigator.serviceWorker.controller&&Qa()})}),setInterval(()=>{e.update(),Dd()},5*60*1e3),Dd()}catch(e){console.error("Service Worker registration failed:",e)}},Qa=()=>{const e=document.getElementById("update-notification");e&&e.remove();const t=document.createElement("div");t.id="update-notification",t.innerHTML=`
    <div style="position: fixed; top: 20px; right: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                color: white; padding: 16px 20px; 
                border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10000; text-align: center; font-family: sans-serif;
                max-width: 300px; animation: slideInRight 0.3s ease-out;">
      <div style="margin-bottom: 12px; font-size: 16px; font-weight: bold;">🚀 发现新版本</div>
      <div style="margin-bottom: 12px; font-size: 14px; opacity: 0.9;">应用已更新，刷新页面获取最新功能</div>
      <div style="display: flex; gap: 8px; justify-content: center;">
        <button id="update-now-btn" style="background: white; color: #667eea; border: none; 
                                           padding: 8px 16px; border-radius: 6px; cursor: pointer;
                                           font-weight: bold; font-size: 14px;">
          立即更新
        </button>
        <button id="update-later-btn" style="background: transparent; color: white; border: 1px solid rgba(255,255,255,0.5); 
                                            padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
          稍后
        </button>
      </div>
    </div>
    <style>
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    </style>
  `,document.body.appendChild(t);const n=document.getElementById("update-now-btn");n&&n.addEventListener("click",()=>{window.location.reload()});const r=document.getElementById("update-later-btn");r&&r.addEventListener("click",()=>{t.remove(),setTimeout(Qa,10*60*1e3)}),setTimeout(()=>{document.getElementById("update-notification")&&(t.remove(),setTimeout(Qa,10*60*1e3))},3e4)},Tw=()=>{let e;window.addEventListener("beforeinstallprompt",t=>{var r,i;t.preventDefault(),e=t;const n=document.createElement("div");n.innerHTML=`
      <div style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); 
                  background: #667eea; color: white; padding: 12px 20px; 
                  border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                  z-index: 1000; text-align: center; font-family: sans-serif;">
        <div style="margin-bottom: 8px;">📱 添加到主屏幕</div>
        <button id="install-btn" style="background: white; color: #667eea; border: none; 
                                       padding: 8px 16px; border-radius: 6px; cursor: pointer;">
          安装应用
        </button>
        <button id="dismiss-btn" style="background: transparent; color: white; border: 1px solid white; 
                                       padding: 8px 16px; border-radius: 6px; cursor: pointer; margin-left: 8px;">
          关闭
        </button>
      </div>
    `,document.body.appendChild(n),(r=document.getElementById("install-btn"))==null||r.addEventListener("click",async()=>{if(e){e.prompt();const s=await e.userChoice;console.log("Install prompt result:",s),e=null,document.body.removeChild(n)}}),(i=document.getElementById("dismiss-btn"))==null||i.addEventListener("click",()=>{document.body.removeChild(n)})})},Dd=async()=>{try{const e="1.0.6",t=await fetch("/version.json",{cache:"no-cache",headers:{"Cache-Control":"no-cache"}});if(t.ok){const r=(await t.json()).version;r!==e&&(console.log(`Version update available: ${e} -> ${r}`),Ga(r))}}catch(e){console.log("Version check failed:",e)}},Ga=e=>{const t=document.getElementById("version-update-notification");t&&t.remove();const n=document.createElement("div");n.id="version-update-notification",n.innerHTML=`
    <div style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%); 
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); 
                color: white; padding: 16px 20px; 
                border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10001; text-align: center; font-family: sans-serif;
                max-width: 320px; animation: slideInDown 0.3s ease-out;">
      <div style="margin-bottom: 12px; font-size: 16px; font-weight: bold;">🔄 发现新版本 v${e}</div>
      <div style="margin-bottom: 12px; font-size: 14px; opacity: 0.9;">后端已更新，建议刷新获取最新功能</div>
      <div style="display: flex; gap: 8px; justify-content: center;">
        <button id="version-update-now-btn" style="background: white; color: #ff6b6b; border: none; 
                                               padding: 8px 16px; border-radius: 6px; cursor: pointer;
                                               font-weight: bold; font-size: 14px;">
          立即刷新
        </button>
        <button id="version-update-later-btn" style="background: transparent; color: white; border: 1px solid rgba(255,255,255,0.5); 
                                                padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px;">
          稍后
        </button>
      </div>
    </div>
    <style>
      @keyframes slideInDown {
        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
      }
    </style>
  `,document.body.appendChild(n);const r=document.getElementById("version-update-now-btn");r&&r.addEventListener("click",()=>{window.location.reload()});const i=document.getElementById("version-update-later-btn");i&&i.addEventListener("click",()=>{n.remove(),setTimeout(()=>Ga(e),60*60*1e3)}),setTimeout(()=>{document.getElementById("version-update-notification")&&(n.remove(),setTimeout(()=>Ga(e),60*60*1e3))},6e4)};if(navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")){const e="1.0.5";localStorage.getItem("appVersion")!==e&&(localStorage.setItem("appVersion",e),window.location.reload())}document.addEventListener("visibilitychange",()=>{!document.hidden&&"serviceWorker"in navigator&&navigator.serviceWorker.controller&&navigator.serviceWorker.getRegistration().then(e=>{e&&e.update()})});Pw();Tw();Bo.createRoot(document.getElementById("root")).render(h.jsx(Ja.StrictMode,{children:h.jsx(kw,{})}));
