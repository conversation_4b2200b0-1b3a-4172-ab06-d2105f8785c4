<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <meta name="description" content="小学数学加减法练习游戏" />
    <meta name="theme-color" content="#5DADE2" />
    <meta name="version" content="1.0.5" />
    <meta name="build-time" content="2025-08-02T17:17:38.281Z" />
    <title>数学小天才 v1.0.6</title>
    
    <!-- PWA Configuration -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- iOS PWA Optimizations -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="数学小天才" />
    <meta name="mobile-web-app-capable" content="yes" />
    
    <!-- iOS Startup Images for faster launch -->
    <link rel="apple-touch-startup-image" href="./apple-touch-icon.png" />
    
    <!-- Preload critical resources -->
    <link rel="preload" href="./sounds/correct1.mp3" as="audio" />
    <link rel="preload" href="./sounds/incorrect1.mp3" as="audio" />
    
    <!-- Apple Touch Icons for iPhone -->
    <link rel="apple-touch-icon" href="./apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="./apple-touch-icon.png" />
    
    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="192x192" href="./icon-192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="./icon-512.png" />
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&display=swap" rel="stylesheet">
    <script type="module" crossorigin src="./assets/index-63cU6Bgl.js"></script>
    <link rel="stylesheet" crossorigin href="./assets/index-DWxUXP61.css">
  <link rel="manifest" href="./manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="./registerSW.js"></script></head>
  <body>
    <div id="root"></div>
  </body>
</html>