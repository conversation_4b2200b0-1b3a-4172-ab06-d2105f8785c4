// 测试新的加减法规则
const { LEVELS, generateQuestionForLevel } = require('./dist/assets/index-63cU6Bgl.js');

console.log('🧮 测试新的加减法规则\n');

// 测试每个级别
const testLevels = [
  'level-1-1', 'level-1-2', // 10以内加法
  'level-2-1', 'level-2-2', // 20以内加法
  'level-3-1', 'level-3-2', // 10以内加减法
  'level-4-1', 'level-4-2'  // 20以内加减法
];

testLevels.forEach(levelId => {
  console.log(`\n📊 测试 ${levelId}:`);
  
  // 生成10道题目进行测试
  for (let i = 0; i < 10; i++) {
    try {
      const question = generateQuestionForLevel(levelId);
      const result = question.operand1 + (question.operator === '+' ? question.operand2 : -question.operand2);
      
      console.log(`  ${question.operand1} ${question.operator} ${question.operand2} = ${result}`);
      
      // 验证规则
      if (levelId.startsWith('level-1') && question.operator === '+' && result > 10) {
        console.log(`  ❌ 错误：10以内加法结果超过10`);
      }
      if (levelId.startsWith('level-2') && question.operator === '+' && result > 20) {
        console.log(`  ❌ 错误：20以内加法结果超过20`);
      }
      if (result < 0) {
        console.log(`  ❌ 错误：减法结果为负数`);
      }
    } catch (error) {
      console.log(`  ❌ 生成题目失败: ${error.message}`);
    }
  }
});

console.log('\n✅ 规则测试完成！');
