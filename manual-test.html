<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="小学数学加减法练习游戏">
    <meta name="theme-color" content="#667eea">
    <title>数学小天才 - 测试版本</title>
    
    <!-- PWA Configuration -->
    <link rel="manifest" href="/manifest.json">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="数学小天才">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .card {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 20px;
            color: #333;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 36px;
            border-radius: 25px;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
            min-width: 120px;
        }
        .btn:hover {
            transform: scale(1.05);
        }
        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }
        .btn-success {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
        }
        .question {
            font-size: 48px;
            margin: 20px 0;
        }
        .keypad {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            max-width: 400px;
            margin: 20px auto;
        }
        .key {
            width: 80px;
            height: 80px;
            font-size: 28px;
            font-weight: bold;
        }
        .key.number {
            font-size: 36px;
        }
        .key.action {
            font-size: 16px;
            font-weight: bold;
        }
        .progress-bar {
            background: #ddd;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-fill {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            height: 100%;
            transition: width 0.5s ease;
        }
        .hidden {
            display: none;
        }
        .timer {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .timer.warning {
            color: #ff6b6b;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .stars {
            font-size: 32px;
            margin: 10px 0;
        }
        .result-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .result-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 密码验证页 -->
        <div id="password-page">
            <div class="card" style="max-width: 400px; margin: 50px auto;">
                <h2 style="text-align: center; color: #333; margin-bottom: 30px;">🔐 访问验证</h2>
                <p style="text-align: center; color: #666; margin-bottom: 20px;">请输入访问密码</p>
                
                <div style="text-align: center;">
                    <input type="password" id="password-input" 
                           style="width: 200px; padding: 12px; font-size: 18px; text-align: center; 
                                  border: 2px solid #ddd; border-radius: 10px; margin-bottom: 20px;"
                           placeholder="请输入密码" maxlength="10">
                    <br>
                    <button class="btn" onclick="checkPassword()" style="margin: 10px;">
                        确认
                    </button>
                    <button class="btn btn-secondary" onclick="clearPasswordInput()" style="margin: 10px;">
                        清除
                    </button>
                    <div id="password-error" style="color: #ff6b6b; margin-top: 15px; display: none;">
                        密码错误，请重试
                    </div>
                </div>
            </div>
        </div>

        <!-- 主页 -->
        <div id="home-page" class="hidden">
            <h1>🎯 数学小天才</h1>
            <p>让数学变得更有趣！</p>
            
            <div class="card">
                <h2>📚 练习模式</h2>
                <p>自由练习，巩固基础</p>
                <button class="btn" onclick="selectMode('practice')">开始练习</button>
            </div>
            
            <div class="card">
                <h2>🏆 通关模式</h2>
                <p>闯关挑战，循序渐进</p>
                <button class="btn btn-secondary" onclick="selectMode('adventure')">开始通关</button>
            </div>
            
            <div class="card">
                <h2>⏰ 测试模式</h2>
                <p>限时测试，检验水平</p>
                <button class="btn btn-success" onclick="selectMode('test')">开始测试</button>
            </div>
        </div>

        <!-- 难度选择页 -->
        <div id="difficulty-page" class="hidden">
            <button class="btn" onclick="showPage('home-page')">← 返回</button>
            <h2>选择难度级别</h2>
            <p id="mode-description">当前模式: </p>
            
            <div class="card" onclick="selectDifficulty('1-A')">
                <h3>⭐ 10以内加法(≤10)</h3>
                <p>例：3 + 4 = 7</p>
            </div>
            
            <div class="card" onclick="selectDifficulty('1-B')">
                <h3>⭐⭐ 10以内加法(>10)</h3>
                <p>例：7 + 8 = 15</p>
            </div>
            
            <div class="card" onclick="selectDifficulty('2-A')">
                <h3>⭐⭐ 10以内加减法(≤10)</h3>
                <p>例：8 - 3 = 5</p>
            </div>
            
            <div class="card" onclick="selectDifficulty('2-B')">
                <h3>⭐⭐⭐ 10以内加减法(加法>10)</h3>
                <p>例：6 + 7 = 13, 9 - 4 = 5</p>
            </div>
            
            <div class="card" onclick="selectDifficulty('3')">
                <h3>⭐⭐⭐ 20以内加法</h3>
                <p>例：13 + 6 = 19</p>
            </div>
        </div>

        <!-- 游戏页面 -->
        <div id="game-page" class="hidden">
            <button class="btn" onclick="showPage('difficulty-page')">← 返回</button>
            <h2 id="game-title">练习模式</h2>
            <p id="difficulty-name">10以内加法</p>
            
            <!-- 进度条 -->
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
            <div id="progress-text">第 1 / 10 题</div>
            
            <!-- 倒计时(测试模式) -->
            <div id="timer" class="timer hidden">⏱️ 10:00</div>
            
            <div class="card">
                <div class="question" id="question">3 + 4 = ?</div>
                <div style="font-size: 24px; margin: 20px 0;">你的答案: <span id="answer" style="font-size: 36px; font-weight: bold; color: #333;">_</span></div>
                
                <div id="feedback" class="hidden" style="padding: 32px; margin: 24px; border-radius: 20px; font-size: 24px; font-weight: bold; text-align: center; border: 2px solid;"></div>
                
                <div class="keypad">
                    <button class="key btn number" onclick="inputNumber(1)">1</button>
                    <button class="key btn number" onclick="inputNumber(2)">2</button>
                    <button class="key btn number" onclick="inputNumber(3)">3</button>
                    <button class="key btn number" onclick="inputNumber(4)">4</button>
                    <button class="key btn number" onclick="inputNumber(5)">5</button>
                    <button class="key btn number" onclick="inputNumber(6)">6</button>
                    <button class="key btn number" onclick="inputNumber(7)">7</button>
                    <button class="key btn number" onclick="inputNumber(8)">8</button>
                    <button class="key btn number" onclick="inputNumber(9)">9</button>
                    <button class="key btn action" onclick="clearAnswer()">清除</button>
                    <button class="key btn number" onclick="inputNumber(0)">0</button>
                    <button class="key btn action" onclick="checkAnswer()">确定</button>
                </div>
                
                <div id="game-controls">
                    <button class="btn" onclick="skipQuestion()" id="skip-btn">跳过</button>
                    <button class="btn" onclick="nextQuestion()" id="next-btn" style="display: none;">下一题</button>
                </div>
            </div>
        </div>

        <!-- 测试准备页 -->
        <div id="test-prep-page" class="hidden">
            <button class="btn" onclick="showPage('difficulty-page')">← 返回</button>
            <h2>⏰ 测试模式准备</h2>
            
            <div class="card">
                <h3>准备开始测试</h3>
                <div style="text-align: left; max-width: 400px; margin: 20px auto;">
                    <p>📝 总共 20 道题</p>
                    <p>⏱️ 限时 10 分钟</p>
                    <p>🚫 不能返回修改答案</p>
                    <p>📊 完成后查看详细报告</p>
                </div>
                <button class="btn btn-success" onclick="startTest()">开始测试</button>
            </div>
        </div>

        <!-- 结果页 -->
        <div id="result-page" class="hidden">
            <div class="card">
                <h2 id="result-title">🎉 完成！</h2>
                <div id="stars" class="stars"></div>
                
                <div class="result-grid" id="result-grid">
                    <div class="result-item">
                        <div style="font-size: 24px; font-weight: bold; color: #007bff;" id="accuracy">0%</div>
                        <div>正确率</div>
                    </div>
                    <div class="result-item">
                        <div style="font-size: 24px; font-weight: bold; color: #28a745;" id="correct-count">0/0</div>
                        <div>答对题数</div>
                    </div>
                </div>
                
                <div style="margin: 20px 0;">
                    <button class="btn" onclick="restart()">重新开始</button>
                    <button class="btn btn-secondary" onclick="showPage('home-page')">返回首页</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            mode: '',
            difficulty: '',
            currentQuestion: 0,
            questions: [],
            answers: [],
            timeLeft: 600,
            timer: null
        };

        // 音效管理
        class SoundManager {
            constructor() {
                this.enabled = true;
                this.audioContext = null;
                this.isUnlocked = false;
                this.initAudioContext();
                this.setupUserInteractionUnlock();
            }

            async initAudioContext() {
                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                } catch (error) {
                    console.warn('Audio not supported');
                }
            }

            setupUserInteractionUnlock() {
                const unlockAudio = async () => {
                    if (this.audioContext && this.audioContext.state === 'suspended') {
                        try {
                            await this.audioContext.resume();
                            // 播放一个静音音频来真正解锁
                            const oscillator = this.audioContext.createOscillator();
                            const gainNode = this.audioContext.createGain();
                            oscillator.connect(gainNode);
                            gainNode.connect(this.audioContext.destination);
                            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                            oscillator.start(this.audioContext.currentTime);
                            oscillator.stop(this.audioContext.currentTime + 0.01);
                            
                            this.isUnlocked = true;
                            console.log('Audio unlocked for Safari');
                            
                            // 显示音频已解锁的提示
                            this.showAudioUnlockedNotification();
                        } catch (error) {
                            console.warn('Failed to unlock audio:', error);
                        }
                    }
                };

                // 监听各种用户交互事件来解锁音频
                ['touchstart', 'touchend', 'mousedown', 'keydown', 'click'].forEach(event => {
                    document.addEventListener(event, unlockAudio, { once: true });
                });
            }

            showAudioUnlockedNotification() {
                // 在移动设备上显示音频已解锁的提示
                const isMobile = /iPhone|iPad|iPod|Android|Mobile|Safari/.test(navigator.userAgent);
                if (isMobile) {
                    const notification = document.createElement('div');
                    notification.id = 'audio-notification';
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #4CAF50;
                        color: white;
                        padding: 12px 16px;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: bold;
                        z-index: 99999;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.4);
                        border: 2px solid #45a049;
                        font-family: 'Comic Sans MS', cursive;
                        animation: slideIn 0.3s ease-out;
                    `;
                    
                    // 添加动画CSS
                    if (!document.getElementById('notification-style')) {
                        const style = document.createElement('style');
                        style.id = 'notification-style';
                        style.textContent = `
                            @keyframes slideIn {
                                from { transform: translateX(100%); opacity: 0; }
                                to { transform: translateX(0); opacity: 1; }
                            }
                            @keyframes slideOut {
                                from { transform: translateX(0); opacity: 1; }
                                to { transform: translateX(100%); opacity: 0; }
                            }
                        `;
                        document.head.appendChild(style);
                    }
                    
                    notification.innerHTML = '🔊 音效已启用';
                    document.body.appendChild(notification);
                    
                    // 3秒后滑出
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            notification.style.animation = 'slideOut 0.3s ease-in';
                            setTimeout(() => {
                                if (document.body.contains(notification)) {
                                    document.body.removeChild(notification);
                                }
                            }, 300);
                        }
                    }, 3000);
                    
                    console.log('Audio notification shown for mobile device');
                }
            }

            async playCorrectSound() {
                if (!this.enabled || !this.audioContext) return;
                
                try {
                    // Safari需要先解锁音频上下文
                    if (this.audioContext.state === 'suspended') {
                        await this.audioContext.resume();
                    }
                    
                    // 确保音频已解锁
                    if (!this.isUnlocked) {
                        console.warn('Audio not unlocked yet');
                        return;
                    }

                    // 播放愉快的三音符和弦
                    const frequencies = [523.25, 659.25, 783.99]; // C-E-G
                    frequencies.forEach((freq, index) => {
                        const oscillator = this.audioContext.createOscillator();
                        const gainNode = this.audioContext.createGain();
                        
                        oscillator.connect(gainNode);
                        gainNode.connect(this.audioContext.destination);
                        
                        oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime);
                        oscillator.type = 'sine';
                        
                        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                        gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.01);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
                        
                        oscillator.start(this.audioContext.currentTime);
                        oscillator.stop(this.audioContext.currentTime + 0.3);
                    });
                } catch (error) {
                    console.warn('Sound play failed:', error);
                }
            }

            async playIncorrectSound() {
                if (!this.enabled || !this.audioContext) return;
                
                try {
                    if (this.audioContext.state === 'suspended') {
                        await this.audioContext.resume();
                    }
                    
                    if (!this.isUnlocked) {
                        console.warn('Audio not unlocked yet');
                        return;
                    }

                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);
                    
                    oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
                    oscillator.frequency.linearRampToValueAtTime(150, this.audioContext.currentTime + 0.3);
                    oscillator.type = 'sine';
                    
                    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(0.2, this.audioContext.currentTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
                    
                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + 0.3);
                } catch (error) {
                    console.warn('Sound play failed:', error);
                }
            }

            async playClickSound() {
                if (!this.enabled || !this.audioContext) return;
                
                try {
                    if (this.audioContext.state === 'suspended') {
                        await this.audioContext.resume();
                    }
                    
                    if (!this.isUnlocked) return; // 静默失败，点击音效不是必需的

                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);
                    
                    oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
                    oscillator.type = 'square';
                    
                    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
                    
                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + 0.1);
                } catch (error) {
                    console.warn('Sound play failed:', error);
                }
            }

            async playCompleteSound() {
                if (!this.enabled || !this.audioContext) return;
                
                try {
                    if (this.audioContext.state === 'suspended') {
                        await this.audioContext.resume();
                    }
                    
                    if (!this.isUnlocked) {
                        console.warn('Audio not unlocked yet');
                        return;
                    }

                    // 播放上升音阶
                    const notes = [523.25, 587.33, 659.25, 698.46, 783.99, 880.00, 987.77, 1046.50];
                    notes.forEach((freq, index) => {
                        setTimeout(() => {
                            const oscillator = this.audioContext.createOscillator();
                            const gainNode = this.audioContext.createGain();
                            
                            oscillator.connect(gainNode);
                            gainNode.connect(this.audioContext.destination);
                            
                            oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime);
                            oscillator.type = 'sine';
                            
                            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                            gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.01);
                            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);
                            
                            oscillator.start(this.audioContext.currentTime);
                            oscillator.stop(this.audioContext.currentTime + 0.2);
                        }, index * 100);
                    });
                } catch (error) {
                    console.warn('Sound play failed:', error);
                }
            }
        }

        const soundManager = new SoundManager();

        // 难度配置
        const difficulties = {
            'level-1': { name: '10以内加法', range: [1, 9], ops: ['+'], limit: 10 },
            'level-2': { name: '20以内加法', range: [1, 19], ops: ['+'], limit: 20 },
            'level-3': { name: '10以内加减法', range: [1, 10], ops: ['+', '-'], limit: 10 },
            'level-4': { name: '20以内加减法', range: [1, 20], ops: ['+', '-'], limit: 20 },
            'level-5': { name: '100以内加法', range: [1, 100], ops: ['+'], limit: null },
            'level-6': { name: '100以内加减法', range: [1, 100], ops: ['+', '-'], limit: null }
        };

        let currentAnswer = '';
        let isAuthenticated = false;
        const correctPassword = '0401';

        function showPage(pageId) {
            document.querySelectorAll('.container > div').forEach(page => {
                page.classList.add('hidden');
            });
            document.getElementById(pageId).classList.remove('hidden');
        }

        function checkPassword() {
            const inputPassword = document.getElementById('password-input').value;
            const errorDiv = document.getElementById('password-error');
            
            if (inputPassword === correctPassword) {
                isAuthenticated = true;
                sessionStorage.setItem('mathGameAuth', 'true');
                showPage('home-page');
                errorDiv.style.display = 'none';
            } else {
                errorDiv.style.display = 'block';
                document.getElementById('password-input').value = '';
                document.getElementById('password-input').focus();
                
                // 3秒后隐藏错误信息
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 3000);
            }
        }

        function clearPasswordInput() {
            document.getElementById('password-input').value = '';
            document.getElementById('password-error').style.display = 'none';
        }

        function checkAuthentication() {
            // 检查会话存储中的认证状态
            const authStatus = sessionStorage.getItem('mathGameAuth');
            if (authStatus === 'true') {
                isAuthenticated = true;
                showPage('home-page');
            } else {
                showPage('password-page');
            }
        }

        function selectMode(mode) {
            if (!isAuthenticated) {
                showPage('password-page');
                return;
            }
            gameState.mode = mode;
            document.getElementById('mode-description').textContent = 
                `当前模式: ${mode === 'practice' ? '练习模式' : mode === 'adventure' ? '通关模式' : '测试模式'}`;
            showPage('difficulty-page');
        }

        function selectDifficulty(difficulty) {
            gameState.difficulty = difficulty;
            
            if (gameState.mode === 'test') {
                showPage('test-prep-page');
            } else {
                startGame();
            }
        }

        function startTest() {
            gameState.timeLeft = 600;
            startGame();
            
            // 启动倒计时
            document.getElementById('timer').classList.remove('hidden');
            gameState.timer = setInterval(() => {
                gameState.timeLeft--;
                updateTimer();
                if (gameState.timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }

        function updateTimer() {
            const minutes = Math.floor(gameState.timeLeft / 60);
            const seconds = gameState.timeLeft % 60;
            const timeStr = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            const timerEl = document.getElementById('timer');
            timerEl.textContent = `⏱️ ${timeStr}`;
            
            if (gameState.timeLeft <= 60) {
                timerEl.classList.add('warning');
            }
        }

        function generateQuestion(difficulty) {
            const config = difficulties[difficulty];
            const [min, max] = config.range;
            const op = config.ops[Math.floor(Math.random() * config.ops.length)];
            
            let num1, num2, answer;
            
            do {
                num1 = Math.floor(Math.random() * (max - min + 1)) + min;
                if (op === '+') {
                    num2 = Math.floor(Math.random() * (max - min + 1)) + min;
                    answer = num1 + num2;
                } else {
                    num2 = Math.floor(Math.random() * num1) + 1;
                    answer = num1 - num2;
                }
            } while (config.limit && answer > config.limit || answer < 0);
            
            return { num1, num2, op, answer };
        }

        function startGame() {
            gameState.currentQuestion = 0;
            gameState.answers = [];
            
            const questionCount = gameState.mode === 'test' ? 20 : 10;
            gameState.questions = [];
            
            for (let i = 0; i < questionCount; i++) {
                gameState.questions.push(generateQuestion(gameState.difficulty));
            }
            
            document.getElementById('game-title').textContent = 
                gameState.mode === 'practice' ? '📚 练习模式' : 
                gameState.mode === 'adventure' ? '🏆 通关模式' : '⏰ 测试模式';
            
            document.getElementById('difficulty-name').textContent = difficulties[gameState.difficulty].name;
            
            if (gameState.mode !== 'test') {
                document.getElementById('timer').classList.add('hidden');
            }
            
            showPage('game-page');
            showQuestion();
        }

        function showQuestion() {
            const q = gameState.questions[gameState.currentQuestion];
            document.getElementById('question').textContent = `${q.num1} ${q.op} ${q.num2} = ?`;
            document.getElementById('progress-text').textContent = 
                `第 ${gameState.currentQuestion + 1} / ${gameState.questions.length} 题`;
            
            const progress = ((gameState.currentQuestion) / gameState.questions.length) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
            
            clearAnswer();
            document.getElementById('feedback').classList.add('hidden');
            
            // 重新显示键盘和跳过按钮，隐藏下一题按钮
            document.querySelector('.keypad').style.display = 'grid';
            document.getElementById('skip-btn').style.display = 'inline-block';
            document.getElementById('next-btn').style.display = 'none';
        }

        function inputNumber(num) {
            // 强制尝试解锁音频（针对移动设备）
            if (!soundManager.isUnlocked) {
                soundManager.setupUserInteractionUnlock();
            }
            
            if (currentAnswer.length < 3) {
                soundManager.playClickSound(); // 播放点击音效
                currentAnswer += num;
                document.getElementById('answer').textContent = currentAnswer || '_';
            }
        }

        function clearAnswer() {
            currentAnswer = '';
            document.getElementById('answer').textContent = '_';
        }

        function checkAnswer() {
            if (!currentAnswer) return;
            
            const q = gameState.questions[gameState.currentQuestion];
            const userAnswer = parseInt(currentAnswer);
            const correct = userAnswer === q.answer;
            
            gameState.answers.push({ question: q, userAnswer, correct });
            
            const feedback = document.getElementById('feedback');
            feedback.classList.remove('hidden');
            
            if (correct) {
                feedback.style.background = '#d4edda';
                feedback.style.color = '#155724';
                feedback.style.borderColor = '#c3e6cb';
                feedback.textContent = '✅ 答对了！';
                soundManager.playCorrectSound(); // 播放答对音效
            } else {
                feedback.style.background = '#f8d7da';
                feedback.style.color = '#721c24';
                feedback.style.borderColor = '#f5c6cb';
                feedback.textContent = `❌ 正确答案是 ${q.answer}`;
                soundManager.playIncorrectSound(); // 播放答错音效
            }
            
            // 隐藏键盘和跳过按钮，显示下一题按钮
            document.querySelector('.keypad').style.display = 'none';
            document.getElementById('skip-btn').style.display = 'none';
            document.getElementById('next-btn').style.display = 'inline-block';
            document.getElementById('next-btn').classList.remove('hidden');
        }

        function skipQuestion() {
            // 练习模式限制跳过次数
            if (gameState.mode === 'practice' && gameState.currentQuestion >= 9) {
                alert('已经是最后一题了，不能再跳过！');
                return;
            }
            
            const q = gameState.questions[gameState.currentQuestion];
            gameState.answers.push({ question: q, userAnswer: null, correct: false });
            
            // 显示跳过结果
            const feedback = document.getElementById('feedback');
            feedback.classList.remove('hidden');
            feedback.style.background = '#fff3cd';
            feedback.style.color = '#856404';
            feedback.style.borderColor = '#ffeaa7';
            feedback.textContent = '⏭️ 已跳过此题';
            
            // 隐藏键盘和跳过按钮，显示下一题按钮
            document.querySelector('.keypad').style.display = 'none';
            document.getElementById('skip-btn').style.display = 'none';
            document.getElementById('next-btn').style.display = 'inline-block';
            document.getElementById('next-btn').classList.remove('hidden');
        }

        function nextQuestion() {
            gameState.currentQuestion++;
            
            if (gameState.currentQuestion >= gameState.questions.length) {
                endGame();
            } else {
                showQuestion();
            }
        }

        function endGame() {
            if (gameState.timer) {
                clearInterval(gameState.timer);
                gameState.timer = null;
            }
            
            const correct = gameState.answers.filter(a => a.correct).length;
            const total = gameState.answers.length;
            const accuracy = Math.round((correct / total) * 100);
            
            document.getElementById('accuracy').textContent = accuracy + '%';
            document.getElementById('correct-count').textContent = `${correct}/${total}`;
            
            // 星级评定
            let stars = 0;
            if (accuracy >= 100) stars = 3;
            else if (accuracy >= 90) stars = 2;
            else if (accuracy >= 80) stars = 1;
            
            document.getElementById('stars').textContent = '⭐'.repeat(stars) + '☆'.repeat(3 - stars);
            
            let title = '';
            if (gameState.mode === 'adventure') {
                title = stars >= 2 ? '🎉 通关成功！' : '💪 继续努力！';
            } else if (gameState.mode === 'test') {
                title = accuracy >= 90 ? '🏆 优秀！' : accuracy >= 70 ? '👍 良好！' : '📚 需要加强！';
            } else {
                title = '🎉 练习完成！';
            }
            
            document.getElementById('result-title').textContent = title;
            soundManager.playCompleteSound(); // 播放完成音效
            showPage('result-page');
        }

        function restart() {
            if (gameState.mode === 'test') {
                showPage('test-prep-page');
            } else {
                startGame();
            }
        }

        // PWA功能
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => console.log('SW registered'))
                .catch(error => console.log('SW registration failed'));
        }

        // Install prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // 显示安装提示
            const installBanner = document.createElement('div');
            installBanner.innerHTML = `
                <div style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); 
                            background: #667eea; color: white; padding: 12px 20px; 
                            border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                            z-index: 1000; text-align: center; font-family: 'Comic Sans MS', cursive;">
                    <div style="margin-bottom: 8px;">📱 添加到主屏幕</div>
                    <button onclick="installApp()" style="background: white; color: #667eea; border: none; 
                                                   padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                        安装应用
                    </button>
                    <button onclick="dismissInstall()" style="background: transparent; color: white; border: 1px solid white; 
                                                   padding: 8px 16px; border-radius: 6px; cursor: pointer; margin-left: 8px;">
                        关闭
                    </button>
                </div>
            `;
            installBanner.id = 'install-banner';
            document.body.appendChild(installBanner);
        });

        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((result) => {
                    console.log('Install result:', result);
                    deferredPrompt = null;
                    dismissInstall();
                });
            }
        }

        function dismissInstall() {
            const banner = document.getElementById('install-banner');
            if (banner) {
                document.body.removeChild(banner);
            }
        }

        // 支持回车键确认密码
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password-input');
            passwordInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    checkPassword();
                }
            });
        });

        // 音频测试函数
        function testAudio() {
            console.log('Testing audio...', soundManager.isUnlocked);
            soundManager.showAudioUnlockedNotification();
            if (soundManager.isUnlocked) {
                soundManager.playCorrectSound();
            } else {
                alert('音频未解锁，请先点击任意数字按钮');
            }
        }

        // 检测移动设备并显示音频测试按钮
        function setupMobileAudioTest() {
            const isMobile = /iPhone|iPad|iPod|Android|Mobile/.test(navigator.userAgent);
            if (isMobile) {
                // 在所有页面添加音频测试按钮
                const testButton = document.createElement('div');
                testButton.innerHTML = `
                    <button onclick="testAudio()" style="
                        position: fixed; 
                        bottom: 20px; 
                        left: 20px; 
                        background: #ff9800; 
                        color: white; 
                        border: none; 
                        padding: 10px 15px; 
                        border-radius: 20px; 
                        font-size: 12px;
                        z-index: 1000;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    ">
                        🔊 测试音效
                    </button>
                `;
                document.body.appendChild(testButton);
            }
        }

        // 初始化 - 检查认证状态
        checkAuthentication();
        
        // 设置移动端音频测试
        setupMobileAudioTest();
    </script>
</body>
</html>