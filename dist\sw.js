if(!self.define){let e,n={};const i=(i,c)=>(i=new URL(i+".js",c).href,n[i]||new Promise(n=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=n,document.head.appendChild(e)}else e=i,importScripts(i),n()}).then(()=>{let e=n[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(c,s)=>{const r=e||("document"in self?document.currentScript.src:"")||location.href;if(n[r])return;let o={};const d=e=>i(e,r),a={module:{uri:r},exports:o,require:d};n[r]=Promise.all(c.map(e=>a[e]||d(e))).then(e=>(s(...e),o))}}define(["./workbox-74f2ef77"],function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"apple-touch-icon.png",revision:"4e0fadb2dd98ca20c124df3ad8eac225"},{url:"assets/index-63cU6Bgl.js",revision:null},{url:"assets/index-DWxUXP61.css",revision:null},{url:"icon-192.png",revision:"928c88673ec3ad09b780879bafde35b0"},{url:"icon-512.png",revision:"11cfbc8bdfe5e5e2570def25d1d1a3e2"},{url:"icon.svg",revision:"e03d199ffe222184cd3b84588e01c95c"},{url:"index.html",revision:"9ebee5a9d06c2f1e2ca993a1cff8a6a7"},{url:"registerSW.js",revision:"402b66900e731ca748771b6fc5e7a068"},{url:"apple-touch-icon.png",revision:"4e0fadb2dd98ca20c124df3ad8eac225"},{url:"icon-192.png",revision:"928c88673ec3ad09b780879bafde35b0"},{url:"icon-512.png",revision:"11cfbc8bdfe5e5e2570def25d1d1a3e2"},{url:"manifest.webmanifest",revision:"d7ec25cb3c8d048094131ed4af0a163c"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/\.(?:png|jpg|jpeg|svg|gif|webp)$/,new e.CacheFirst({cacheName:"images-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:2592e3})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/,new e.CacheFirst({cacheName:"audio-cache",plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:2592e3})]}),"GET")});
