# 数学小天才 - 小学数学加减法练习游戏

一个专为小学生设计的数学加减法练习游戏，支持多种难度级别和游戏模式。

## 功能特点

### 🎯 六个难度级别
- **级别1**: 10以内加法（两数相加≤10）
- **级别2**: 20以内加法（两数相加≤20）
- **级别3**: 10以内加减法（加法≤10）
- **级别4**: 20以内加减法（加法≤20）
- **级别5**: 100以内加法
- **级别6**: 100以内加减法

### 🎮 三种游戏模式
- **练习模式**: 自由练习，无时间限制
- **通关模式**: 关卡式挑战，循序渐进
- **错题本**: 专门练习错误题目

### ✨ 特色功能
- 🎨 色彩丰富的儿童友好界面
- 🎭 流畅的动画效果和反馈
- 📊 详细的学习进度追踪
- 🏆 星级评价和成就系统
- 💾 本地数据存储
- 📱 移动端完美适配

## 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式**: Tailwind CSS
- **动画**: Framer Motion
- **存储**: localStorage

## 开发环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn

## 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd math-practice-game
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **构建生产版本**
   ```bash
   npm run build
   ```

5. **预览生产版本**
   ```bash
   npm run preview
   ```

## 项目结构

```
src/
├── components/          # 可复用UI组件
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Layout.tsx
│   ├── NumberDisplay.tsx
│   ├── NumberKeypad.tsx
│   ├── ProgressBar.tsx
│   ├── StarRating.tsx
│   ├── FeedbackAnimation.tsx
│   └── ParticleEffect.tsx
├── pages/              # 页面组件
│   ├── HomePage.tsx
│   ├── DifficultySelection.tsx
│   └── PracticeMode.tsx
├── types/              # TypeScript类型定义
│   └── index.ts
├── utils/              # 工具函数
│   ├── questionGenerator.ts
│   └── storage.ts
├── hooks/              # 自定义Hook
│   └── useGameStorage.ts
├── App.tsx             # 主应用组件
├── main.tsx           # 应用入口
└── index.css          # 全局样式
```

## 核心功能说明

### 题目生成算法
- 根据难度级别自动生成合适的数学题目
- 支持加法和减法运算
- 可配置数字范围和结果限制

### 数据存储
- 用户学习统计数据
- 练习进度和成绩
- 错题收集
- 个人设置

### 动画效果
- 答题反馈动画
- 粒子效果
- 页面切换动画
- 组件交互动画

## 浏览器兼容性

- Chrome 80+
- Safari 13+
- Firefox 75+
- Edge 80+

## 移动端支持

- 响应式设计，完美适配手机和平板
- 触摸友好的交互设计
- PWA特性支持离线使用

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 更新日志

### v1.0.0
- 实现基础的练习模式
- 支持7个难度级别
- 完整的数据存储功能
- 动画效果和用户反馈
- 移动端适配